import { IPollQuestionFirstClickTest } from "@/entities/models/poll-question/questionTypes/firstClickTest.types";

import {
  ITranslation,
  QuestionBaseFieldsTranslation,
  QuestionBaseFieldsTranslationResult,
  QuestionSavedTranslation,
  TextFieldTranslation,
  TextFieldTranslationResult,
  TranslationConfig,
} from "../types";
import {
  QuestionTextFieldResult,
  QuestionTextFieldTranslation,
  updateQuestionTextFieldTranslation,
} from "./TextFieldTranslation";
import { QuestionTranslation } from "./QuestionTranslation";
import { DEFAULT_TEXTS } from "@/dialogs/poll-translate-sidesheet/constants/defaultTexts";


type IFirstClick = {
  id: string;
  button_text: string;
};

type QuestionFirstClickTestTranslationFields = QuestionBaseFieldsTranslation & {
  skipText?: TextFieldTranslation;
  commentLabel?: TextFieldTranslation;
  commentPlaceholder?: TextFieldTranslation;
  buttonText?: TextFieldTranslation;
};

type QuestionFirstClickTestTranslationResult =
  QuestionBaseFieldsTranslationResult & {
    placeholder_text?: TextFieldTranslationResult;
    skip_text?: TextFieldTranslationResult;
    firstClick?: IFirstClick;
  };

type QuestionFirstClickTestSavedTranslation = QuestionSavedTranslation & {
  skip_text?: string;
  comment?: Comment;
  firstClickLang?: IFirstClick;
};

export class QuestionFirstClickTestTranslation
  extends QuestionTranslation
  implements ITranslation
{
  fields: QuestionFirstClickTestTranslationFields;
  interscreenId: string;

  constructor(config: TranslationConfig) {
    super(config);

    const question = config.question as IPollQuestionFirstClickTest;

		if (!question) {
      this.interscreenId = "";
      return;
    }
		

    const { skip, skipText, firstClick } = question || {};
    const { id = "", button_text } = firstClick || {};

    this.interscreenId = id;

    if (skip) {
      this.fields.skipText = QuestionTextFieldTranslation(
        skipText || DEFAULT_TEXTS.skipTextVariants
      );
    }

      this.fields.buttonText = QuestionTextFieldTranslation(
        button_text || DEFAULT_TEXTS.showImageButtonText
      );
  }

  get hasComment(): boolean {
    return true;
  }

  getData(): QuestionFirstClickTestTranslationResult {
    const firstClick: IFirstClick = {
      id: this.interscreenId || "",
      button_text: QuestionTextFieldResult(this.fields.buttonText),
    };

    const result: QuestionFirstClickTestTranslationResult = {
      ...super.getData(),
      firstClick: firstClick,
    };

    if (this.fields.skipText) {
      result.skip_text = QuestionTextFieldResult(this.fields.skipText);
    }

    return result;
  }

  updateTranslation(data: QuestionFirstClickTestSavedTranslation): void {
    super.updateTranslation(data);

		if (!data) return; 

		if (data.firstClickLang?.button_text) {
      updateQuestionTextFieldTranslation(
        this.fields.buttonText,
        data.firstClickLang.button_text
      );
    }

    if (data.skip_text !== undefined) {
      updateQuestionTextFieldTranslation(this.fields.skipText, data.skip_text);
    }

    if (data.comment_label !== undefined) {
      updateQuestionTextFieldTranslation(
        this.fields.commentLabel,
        data.comment_label
      );
    }

    if (data.placeholder_text !== undefined) {
      updateQuestionTextFieldTranslation(
        this.fields.commentPlaceholder,
        data.placeholder_text
      );
    }
  }
}

// ko/pages/poll/stats/question-types/first-click-test/index.js

import { Question } from "../models/question";
import "./component";
import "./style.less";

export class FirstClickTestQuestion extends Question {
  constructor(questionData, ctx) {
    super(questionData, ctx);

    // Extract question-specific data
    [
      "statistics",
      "clickAreas",
      "imageUrl",
      "imageWidth",
      "imageHeight"
    ].forEach((key) => {
      this[key] = questionData[key];
    });

    console.log('[FIRST CLICK TEST] statistics', this.statistics);
    console.log('[FIRST CLICK TEST] clickAreas', this.clickAreas);
    console.log('[FIRST CLICK TEST] imageUrl', this.imageUrl);
    console.log('[FIRST CLICK TEST] imageWidth', this.imageWidth);
    console.log('[FIRST CLICK TEST] imageHeight', this.imageHeight);

    // Initialize computed values
    this.totalClicks = this.statistics.totalClicks || 0;
    this.avgExecutionTime = this.statistics.avgExecutionTime || 0;

    // Process click areas with statistics
    this.processedAreas = this.processClickAreas();
    
    // Set up observables for UI interactions
    this.selectedArea = ko.observable(null);
    this.showHeatmap = ko.observable(true);
  }

  // Process click areas with their statistics
  processClickAreas() {
    if (!this.clickAreas || !Array.isArray(this.clickAreas)) {
      console.warn('No click areas defined for question', this.id);
      return [];
    }
    
    return this.clickAreas.map((area, index) => {
      try {
        return {
          ...area,
          clicks: this.statistics?.areaClicks?.[area.id] || 0,
          percentage: this.totalClicks > 0
            ? ((this.statistics?.areaClicks?.[area.id] || 0) / this.totalClicks * 100).toFixed(1)
            : 0,
          color: this.colors[index % this.colors.length]
        };
      } catch (error) {
        console.error('Error processing area', area, error);
        return null;
      }
    }).filter(Boolean);
  }

  // Required: Define chart data getters
  get columnChartData() {
    const data = this.processedAreas.map((area, index) => ({
      name: area.name,
      data: [area.clicks],
      color: area.color
    }));

    // Add user-defined points if they exist
    if (this.statistics.userDefinedPointsClicks > 0) {
      data.push({
        name: 'Пользовательские точки',
        data: [this.statistics.userDefinedPointsClicks],
        color: '#DADFE3'
      });
    }

    // Add skipped responses if they exist
    if (this.statistics.skippedCount > 0) {
      data.push({
        name: 'Респондент отказался от ответа',
        data: [this.statistics.skippedCount],
        color: '#B0BEC5'
      });
    }

    return data;
  }

  get pieChartData() {
    const data = this.processedAreas.map(area => ({
      name: area.name,
      y: area.clicks,
      percentage: area.percentage
    }));

    if (this.statistics.userDefinedPointsClicks > 0) {
      data.push({
        name: 'Пользовательские точки',
        y: this.statistics.userDefinedPointsClicks,
        percentage: this.statistics.userDefinedPointsPercent,
        color: '#DADFE3'
      });
    }

    if (this.statistics.skippedCount > 0) {
      data.push({
        name: 'Респондент отказался от ответа',
        y: this.statistics.skippedCount,
        percentage: this.statistics.skippedPercent,
        color: '#B0BEC5'
      });
    }

    return [{
      name: 'Распределение кликов',
      data: data
    }];
  }

  // Required: Define colors for charts
  get colors() {
    return [
      "#3F51B5", "#536DFE", "#82B1FF", "#84ffff",
      "#aadbff", "#bdb2ff", "#ff9dd8", "#ffbdb4"
    ];
  }

  // Required: Get total response count
  get totalCount() {
    return this.answersCount;
  }

  // Handle area click for detailed view - opens sidesheet similar to variants
  openAreaDetails(area) { // Renamed from openAreaSidesheet to match guide's table click
    this.ctx.openSidesheet("stats-first-click-all-answers-sidesheet", { // Changed to unified sidesheet
      question: this,
      title: `Клики в области "${area.name}"`,
      filterParams: { area_id: area.id }
    });
  }

  // Open sidesheet for user-defined points (clicks outside areas)
  openUserPointsDetails() { // Renamed from openUserPointsSidesheet
    this.ctx.openSidesheet("stats-first-click-all-answers-sidesheet", { // Changed to unified sidesheet
      question: this,
      title: "Пользовательские точки",
      filterParams: { filter_type: 'user_points' }
    });
  }

  // Open sidesheet for skipped responses
  openSkippedDetails() { // Renamed from openSkippedSidesheet
    this.ctx.openSidesheet("stats-first-click-all-answers-sidesheet", { // Changed to unified sidesheet
      question: this,
      title: "Респондент отказался от ответа",
      filterParams: { filter_type: 'skipped' }
    });
  }

  // Open sidesheet for all answers
  openAllAnswersDetails() { // Renamed from openAllAnswersSidesheet
    this.ctx.openSidesheet("stats-first-click-all-answers-sidesheet", {
      question: this,
      title: "Все ответы",
      filterParams: { filter_type: 'all' }
    });
  }
  
  // Open heatmap details sidesheet
  openHeatmapDetailsModal() { // Renamed from openHeatmapSidesheet to match template
    this.ctx.openSidesheet("stats-heatmap-details-sidesheet", {
      question: this,
      title: "Тепловая карта кликов"
    });
  }

  // Method for "Все ответы" button in template
  onAllAnswersClick() {
    this.openAllAnswersDetails();
  }
}

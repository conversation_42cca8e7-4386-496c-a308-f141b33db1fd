<?php

namespace app\modules\foquz\models;

use Yii;

/**
 * This is the model class for table "foquz_question_first_click".
 *
 * @property int $id
 * @property int|null $foquz_question_id
 * @property int|null $mobile_view Отображение на сматрфоне, 0-по ширине, 1-по высоте
 * @property int|null $min_click Min кол-во кликов
 * @property int|null $max_click Max кол-во кликов
 * @property int|null $show_time Время показа изображения, секунд
 * @property string|null $button_text Текст кнопки
 * @property int|null $allow_cancel_click Возможность отменить клик
 *
 * @property FoquzQuestion $question
 * @property FoquzQuestionFirstClickLang[] $langs
 */
class FoquzQuestionFirstClick extends \yii\db\ActiveRecord
{
    /**
     * {@inheritdoc}
     */
    public static function tableName(): string
    {
        return 'foquz_question_first_click';
    }

    /**
     * {@inheritdoc}
     */
    public function rules(): array
    {
        return [
            [['foquz_question_id', 'mobile_view', 'max_click', 'show_time', 'allow_cancel_click'], 'integer'],
            [['button_text'], 'string', 'max' => 255],
            [['foquz_question_id'], 'exist', 'skipOnError' => true, 'targetClass' => FoquzQuestion::class, 'targetAttribute' => ['foquz_question_id' => 'id']],
            [['mobile_view'], 'in', 'range' => [0, 1]],
            [['min_click'], 'integer', 'min' => 1, 'max' => 999, 'tooSmall' => 'Число должно быть положительным', 'tooBig' => 'Максимальное значение 999'],
            [['max_click'], 'integer', 'min' => 1, 'max' => 999, 'tooSmall' => 'Число должно быть положительным', 'tooBig' => 'Максимальное значение 999'],
            [['max_click'], 'compare', 'compareAttribute' => 'min_click', 'operator' => '>=', 'type' => 'number'],
            [['show_time'], 'integer', 'min' => 1, 'max' => 99999, 'tooSmall' => 'Число должно быть положительным', 'tooBig' => 'Максимальное значение 99999'],
            [['allow_cancel_click'], 'in', 'range' => [0, 1]]
        ];
    }

    /**
     * {@inheritdoc}
     */
    public function attributeLabels(): array
    {
        return [
            'id' => 'ID',
            'foquz_question_id' => 'Question ID',
            'mobile_view' => 'Mobile View',
            'min_click' => 'Min Click',
            'max_click' => 'Max Click',
            'show_time' => 'Show Time',
            'button_text' => 'Button Text',
            'allow_cancel_click' => 'Allow Cancel Click',
        ];
    }

    public static function createOrUpdate(array $data, int $foquz_question_id): self
    {
        $m = self::findOne(['foquz_question_id' => $foquz_question_id]);
        if (!$m) {
            $m = new self();
            $m->foquz_question_id = $foquz_question_id;
        }
        $m->mobile_view = $data['mobile_view'];
        $m->min_click = $data['min_click'];
        $m->max_click = $data['max_click'];
        $m->show_time = $data['show_time'];
        $m->button_text = $data['button_text'];
        $m->allow_cancel_click = $data['allow_cancel_click'];

        return $m;
    }

    public function copy(int $id): void
    {
        $firstClick = new FoquzQuestionFirstClick();
        $attributes = $this->attributes;
        unset($attributes['id']);
        $attributes['foquz_question_id'] = $id;
        $firstClick->attributes = $attributes;
        $firstClick->save();

        $rows = [];
        $columns = ['poll_lang_id', 'button_text', 'setting_id'];
        foreach ($this->langs as $lang) {
            $rows[] = [
                $lang->poll_lang_id,
                $lang->button_text,
                $firstClick->id
            ];
        }

        if (!empty($rows)) {
            try {
                Yii::$app->db->createCommand()->batchInsert(FoquzQuestionFirstClickLang::tableName(), $columns,
                    $rows)->execute();
            } catch (\Exception $e) {
                echo "Can't copy rows for " . FoquzQuestionFirstClickLang::tableName() . "\n";
                echo $e->getMessage() . "\n";
            }
        }
    }

    public function fields(): array
    {
        return parent::fields()+[
                'langs' => function() {
                    return $this->langs;
                }
            ];
    }

    /**
     * Gets query for [[Question]].
     *
     * @return \yii\db\ActiveQuery
     */
    public function getQuestion(): \yii\db\ActiveQuery
    {
        return $this->hasOne(FoquzQuestion::class, ['id' => 'foquz_question_id']);
    }

    public function getLangs(): \yii\db\ActiveQuery
    {
        return $this->hasMany(FoquzQuestionFirstClickLang::class, ['setting_id' => 'id']);
    }

    public static function parseAnswer(string|array $answerItem): array
    {
        $answer = [];
        if (is_string($answerItem)) {
            $di = json_decode($answerItem, true);
        } else {
            $di = $answerItem;
        }
        if (isset($di['answer']) && count($di['answer'])) {
            $ret = [];
            foreach ($di['answer'] as $point) {
                $ret[] = str_replace(';', ',', $point);
                /*[$x, $y, $t] = explode(';', $point, 3);
                if (isset($x, $y, $t)) {
                    $ret[] = compact('x', 'y', 't');
                }*/
            }
            $answer = $ret;
        }
        return $answer;
    }
}

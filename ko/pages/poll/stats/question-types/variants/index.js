import { Question } from "../models/question";
import "./component";
import "./style.less";

export class VariantsQuestion extends Question {
  constructor(questionData, ctx) {
    super(questionData, ctx);

    [
      // 'assessmentType',
      // 'clarifyingQuestion',
      // 'clarifyingQuestionVariants',
      // 'forAllRates',
      // 'imageUrls',
      // 'videoUrls',
      // 'text',
      "statistics",
      "variants",
      "variantsType",
    ].forEach((key) => {
      this[key] = questionData[key];
    });

    this.skip = questionData.skip;
    this.skipped = parseInt(this.statistics.skipped) || 0;

    this.selfVariantText = questionData.self_variant_text || "Свой вариант";

    const question_data = window.QUESTIONS.find(i => i.id == questionData.question_id)

    this.variants.map((v, i) => {
      const bigData = question_data?.detail_answers.find(i => i.id == v.id)
      if (bigData) {
        v.file_id = bigData.file_id;
        v.file_url = bigData.file_url;
        v.preview_url = bigData.preview_url;
      }
      
      v.customIndex = i
      return v
    })

    if (questionData.isSelfAnswer) {
      this.variants.push({
        id: 0,
        question: this.selfVariantText,
        legendTableRowClass:
          "question-statistics__question-statistics-legend-table-custom-row",
        customIndex: this.variants.length
      });
    }

    /* const emptyVariant = this.variants.find(v => v.type == 1)

    if (emptyVariant) {
      this.variants = this.variants.filter(v => v.type != 1)
      this.variants.push(emptyVariant)
    } */

    if (questionData.donor) {
      const selfVariant = this.variants.find((v) => v.id === -1);
      if (selfVariant) this.selfVariantText = selfVariant.question;
    }

    this.gallery = questionData.gallery.map((i, index) => {
      return {
        url: i.url,
        poster: i.poster,
        description: i.description,
        position: i.position || index,
      };
    });
    this.gallery.sort((a, b) => a.position - b.position);

    this.value = [
      ...this.statistics.variants,
      this.statistics.customVariants.length,
      this.skipped,
    ];
    
    this.hasRemoved = this.variants.some(v => v.is_deleted == 1);

    this.showRemoved = ko.observable(false);
    this.showChartRemoved = ko.observable(false)


    this.correctVariants = this.getCorrectVariants();
    this.correctVariantsText = this.correctVariants
      .map((v) => v.question)
      .join(", ") || 'не задан';

    this.correctVariantsIds = this.correctVariants.map((v) => v.id);


  }

  get isMultipleVariants() {
    return this.variantsType == 1;
  }

  getMaxPoints(arr) {
    return arr.reduce(
      (acc, current) => acc + +current.points,
      0,
    );
  }

  getCorrectVariants() {
    let variants = [];

    if (!this.withPoints) return variants;

    if (this.isMultipleVariants) {
      variants = this.variants.filter((v) => {
        return v.id && v.points > 0 && v.type != 1
      });
    } else {
      let max = null;

      this.variants.forEach((v) => {
        if (!v.id || v.points <= 0) return;
        if (!max) max = v;
        else {
          if (max.points < v.points) max = v;
        }
      });
      if (max) {
        variants = [max];
      }
    }

    return variants;
  }

  get colors() {
    return [
      "#3F51B5",
      "#536DFE",
      "#82B1FF",
      "#84ffff",
      "#aadbff",
      "#bdb2ff",
      "#ff9dd8",
      "#ffbdb4",
      "#f4e4cd",
      "#e6ffb1",
      "#bbf9ee",
      "#c2cfff",
      "#e7b3ff",
    ];
  }

  get totalCount() {
    let count = this.value.reduce((sum, r, i) => {
      return sum + r;
    }, 0);
    if (this.skip) count += this.skipped;
    return count;
  }

  get columnChartData() {
    const colors = []
    while (colors.length < this.variants.length) {
      colors.push(...this.colors)
    }
    const variants = this.variants.map((variant, index) => {
      if (!this.showChartRemoved() && variant.is_deleted == 1) return null;
      return {
        name: variant.question,
        data: [this.value[variant.customIndex ? variant.customIndex : index]],
        color: this.colors[index]
      }
    }).filter(Boolean);

    if (this.skip) {
      variants.push({
        name: "Респондент отказался от оценки",
        data: [this.skipped],
        color: "#DADFE3",
      });
    }

    return variants;
  }

  get pieChartData() {
    let data = this.variants.map((variant, index) => {
      if (!this.showChartRemoved() && variant.is_deleted == 1) return null;
      return {
        name: variant.question,
        y: this.value[variant.customIndex ? variant.customIndex : index],
      }
    }).filter(Boolean);

    if (this.skip) {
      data.push({
        name: "Респондент отказался от оценки",
        y: this.skipped,
        color: "#DADFE3",
      });
    }

    return [
      {
        data,
      },
    ];
  }

  openVariantModal(variant) {
    console.log({ variant })
    if (variant === "skipped") {
      super._openVariantModalDialog(
        {
          variant: {},
          params: {
            skipped: 1,
          },
          title: ` Клиенты с пропуском ответа`,
        },
        "withPoints"
      );
      return;
    }

    if (variant.id == 0) {
      super._openCommentsModalDialog({
        variant: variant,
      });
      return;
    }

    super._openVariantModalDialog(
      {
        variant: {},
        params: {
          field: "detail_item",
          value: variant.id,
        },
        title: `Клиенты с ответом <div class='title'>${variant.question}</div>`,
        deleted: variant.is_deleted
      },
      "withPoints"
    );
  }

  openVariantsModal() {
    this.ctx.openStatsModal("stats-variants-sidesheet", {
      withPoints: this.withPoints,
      question: this,
      title: "Варианты ответов",
    });
  }
}

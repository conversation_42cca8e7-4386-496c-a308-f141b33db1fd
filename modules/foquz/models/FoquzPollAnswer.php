<?php

namespace app\modules\foquz\models;

use app\components\helpers\DictionariesHelper;
use app\helpers\DateTimeHelper;
use app\helpers\DevHelper;
use app\models\Client;
use app\models\ClientEmail;
use app\models\company\Company;
use app\models\company\CompanyRequestProcessingSettings;
use app\models\DictionaryElement; 
use app\models\Dish;
use app\models\Filial;
use app\models\FilialCategory;
use app\models\Order;
use app\models\User;
use app\modules\foquz\behaviors\HashIdBehavior;
use app\modules\foquz\models\notifications\SiteNotification;
use app\modules\foquz\models\processing\FoquzPollAnswerProcessing;
use app\modules\foquz\models\quotes\FoquzPollLinkQuotes;
use app\modules\foquz\queue\AnswerItemPointsJob;
use app\modules\foquz\queue\AnswerPointsJob;
use app\modules\foquz\queue\AnswerStatusJob;
use app\modules\foquz\queue\notifications\FullAnswerNotificationJob;
use app\modules\foquz\queue\PollNotificationJob;
use app\modules\foquz\queue\webhook\SendWebhookJob;
use app\modules\foquz\services\api\ExtraQuestionService;
use app\modules\foquz\services\custom\CustomQuotaService;
use app\modules\foquz\services\custom\HRManagerService;
use app\modules\foquz\services\mailings\cache\MailingsAfterPollCache;
use app\modules\foquz\services\mailings\MailingsAfterPollService;
use app\modules\foquz\services\notifications\enums\NotificationChannel;
use app\modules\foquz\services\notifications\enums\NotificationType;
use app\modules\foquz\services\notifications\NotificationsPollService;
use donatj\UserAgent\UserAgentParser;
use Symfony\Component\Console\Question\Question;
use Yii;
use yii\behaviors\TimestampBehavior;
use yii\db\ActiveQuery;
use yii\db\Exception;
use yii\db\Query;
use yii\helpers\ArrayHelper;
use yii\helpers\Json;
use yii\queue\amqp_interop\Queue;
use yii\web\BadRequestHttpException;

/**
 * This is the model class for table "{{%foquz_poll_answer}}".
 *
 * @property int $id
 * @property string $created_at
 * @property string $updated_at
 * @property int $client_id
 * @property int $user_id ID пользователя-респондента
 * @property string $auth_key
 * @property int $foquz_poll_id
 * @property string $status
 * @property string $ip_address
 * @property string $os
 * @property string $useragent
 * @property string $custom_fields
 * @property int $contact_id
 * @property int $points
 * @property int $max_points Максимальное количество баллов
 * @property int $answer_filial_id
 * @property string $device
 * @property int $processing_time_in_minutes
 * @property int $language
 * @property bool $user_agreement
 * @property string|null $points_job_id ID задачи на подсчет баллов
 * @property bool $webhook_sent Вебхук отправлен
 *
 * @property FoquzPoll $foquzPoll
 * @property FoquzPollAnswerItem[] $foquzAnswer
 * @property FoquzPollAnswerProcessing $processing
 * @property Filial $answerFilial
 * @property User $user
 * @property string $first_question_showed_at
 * @property int $answer_time Время ответа (в секундах)
 * @property int|null $quote_id Id квоты на прохождение
 * @property PollLang $pollLang
 * @property Order|null $order
 * @property FoquzContact $contact
 * @property FoquzPollMailingListSend[] $mailingListSend
 * @property FoquzComplaint $complaint
 * @property FoquzComplaintFile[] $complaintFiles
 * @property PollLang $pollLanguage
 * @property FoquzPollMailingListSend $answerChannel
 * @property FoquzPollMailingListSend[] $sends
 * @property FoquzPollDishScore $foquzPollDishes
 * @property FoquzCompanyTag[] $tags
 * @property FoquzContactTag[] $answerTags
 * @property FoquzPollAnswerShowedImage[] $showedImage
 * @property-read array $answerContentForEmail
 * @property-read array $answers Ответы на вопросы (для вывода в разделе "Ответы" и при печати)
 * @property-read bool $withoutPoints Нет ответов с баллами
 * @property-read int|null $minPercentScore Минимальная оценка по анкете в процентах
 * @property-read array $variables Переменные для прохождения (ФИО, данные заказа, промокод, интерпретация баллов)
 */
class FoquzPollAnswer extends BaseModel
{
    const STATUS_NEW = 'new';
    const STATUS_EMAIL_OPEN = 'email-open';
    const STATUS_OPEN = 'open';
    const STATUS_IN_PROGRESS = 'in-progress';
    const STATUS_DONE = 'done';

    // time in seconds
    const AVG_TIME_PER_QUESTION = 180;

    const CHANNEL_TYPE_LINK = 'channel_type_link';
    const CHANNEL_TYPE_FOR_CLIENT = 'channel_type_for_client';
    const CHANNEL_TYPE_CHANNEL = 'channel_type_channel';

    public const STAFF_EDIT_FROM_PROCESS = 1; //Редактирование анкеты из прохождения
    public const STAFF_EDIT_FROM_SIDE_SHEET = 2; //Редактирование анкеты из сайдшита


    public function setProcessingTimeByChannelType($type = null, Channel $channel = null): void
    {
        $this->processing_time_in_minutes = $this->getProcessingTimeByChannelType($type, $channel);
    }

    public function getProcessingTimeByChannelType($type = null, Channel $channel = null): ?int
    {
        $poll = $this->foquzPoll;

        if ($type === self::CHANNEL_TYPE_LINK && $poll->processing_time_by_link_in_minutes > 0) {
            return $poll->processing_time_by_link_in_minutes;
        }

        if ($type === self::CHANNEL_TYPE_FOR_CLIENT && $poll->processing_time_for_client_in_minutes > 0) {
            return $poll->processing_time_for_client_in_minutes;
        }

        if ($type === self::CHANNEL_TYPE_CHANNEL && $channel !== null && $channel->processing_time_in_minutes > 0) {
            return $channel->processing_time_in_minutes;
        }

        return $poll->processing_time_in_minutes;
    }

    /**
     * [createForMailingManual создание ответа для автоматических опросов для рассылки]
     * @param  [type] $pollId    [description]
     * @param  [type] $contactId [description]
     * @return [type]            [description]
     */
    public static function createForMailingAuto($pollId, $order, $contact = null, $channel = null)
    {
        $answer = null;
        if ($order) {
            $answer = self::find()->where(['foquz_poll_id' => $pollId, "order_id" => $order->id])->one();
        } else {
            if ($contact) {
                $answer = self::find()->where(['foquz_poll_id' => $pollId, "contact_id" => $contact->id])->one();
            }
        }

        if (!$answer) {
            $answer = new self;
            $answer->foquz_poll_id = $pollId;
            $answer->status = self::STATUS_NEW;
            $answer->order_id = $order ? $order->id : 0;
            $answer->auth_key = md5(time() . uniqid());
            $answer->answer_filial_id = $order->filial_id;
            if ($order) {
                $answer->client_id = $order ? ($order->client ? $order->client->id : null) : null;
            } else {
                if ($contact) {
                    $answer->client_id = $contact->client_id;
                }
            }
            if ($order) {
                if ($order->client && $order->client->contact) {
                    $answer->contact_id = $order->client->contact->id;
                }
            } else {
                if ($contact) {
                    $answer->contact_id = $contact->id;
                }
            }

            $answer->order_sum = $order ? $order->sum : 0;
            $answer->delivery_id = $order ? $order->delivery_type : null;

            if ($channel) {
                $answer->setProcessingTimeByChannelType(self::CHANNEL_TYPE_CHANNEL, $channel);
            }

            $answer->save();

            if ($order) {
                foreach ($order->dishes as $dish) {
                    if ($dish->sum > 20) {
                        $s = (new FoquzPollDishScore([
                            'answer_id' => $answer->id,
                            'dish_id'   => $dish->dish_id,
                            'sum'       => $dish->sum,
                            'score'     => 0,
                        ]))->save();
                    }


                }
            }
        }

        return $answer;
    }

    public static function filterByAnswer($filterSetting, $poll_id): array
    {
        $filterAnswerID = [];
        $filterQuery = FoquzPollAnswerItem::find()->select('foquz_poll_answer_id');
        $i = 0;
        if (is_object($filterSetting)) {
            $filterSetting = (array)$filterSetting;
        }
        if (!isset($filterSetting[0])) {
            $filterSetting = [$filterSetting];
        }
        foreach ($filterSetting as $setting) {
            if (isset($setting['active']) && !$setting['active']) {
                continue;
            }
            $questionFilterQuery = clone $filterQuery;
            switch ($setting['action']) {
                case FoquzPollStatFilterSettings::ACTION_SELECT_VARIANT:
                    if (empty($setting['variants'])) {
                        continue 2;
                    }
                    $conditions = [];
                    foreach ($setting['variants'] as $variant) {
                        if ((int)$variant !== 0) {
                            $conditions[] = ['like', 'detail_item', $variant];
                        } else {
                            $conditions[] = ['AND', ['NOT', ['self_variant' => '']], ['NOT', ['self_variant' => null]]];
                        }
                    }
                    $questionFilterQuery->andWhere(['foquz_question_id' => $setting['question_id']]);
                    if ((int)$setting['condition'] === FoquzPollStatFilterSettings::CONDITION_AND) {
                        $questionFilterQuery->andWhere(ArrayHelper::merge(['AND'], $conditions));
                    } else {
                        $questionFilterQuery->andWhere(ArrayHelper::merge(['OR'], $conditions));
                    }
                    break;
                case FoquzPollStatFilterSettings::ACTION_NOT_SELECT_VARIANT:
                    if (empty($setting['variants'])) {
                        continue 2;
                    }
                    $conditions = [];
                    foreach ($setting['variants'] as $variant) {
                        if ((int)$variant !== 0) {
                            $conditions[] = ['not like', 'detail_item', $variant];
                        } else {
                            $conditions[] = ['OR', ['self_variant' => ''], ['self_variant' => null]];
                        }
                    }
                    $questionFilterQuery->andWhere(['foquz_question_id' => $setting['question_id']]);
                    if ((int)$setting['condition'] === FoquzPollStatFilterSettings::CONDITION_AND) {
                        $questionFilterQuery->andWhere(ArrayHelper::merge(['AND'], $conditions));
                    } else {
                        $questionFilterQuery->andWhere(ArrayHelper::merge(['OR'], $conditions));
                    }
                    break;
                case FoquzPollStatFilterSettings::ACTION_SKIP_QUESTION:
                    $questionFilterQuery = self::find()->select('id');
                    $questionFilterQuery->andWhere(['foquz_poll_id' => $poll_id]);
                    $questionFilterQuery->andWhere([
                        'NOT',
                        [
                            'id' => FoquzPollAnswerItem::find()
                                ->select('foquz_poll_answer_id')
                                ->where(['foquz_question_id' => $setting['question_id']])
                        ]
                    ]);
                    break;
                case FoquzPollStatFilterSettings::ACTION_SKIP_OPTION:
                    $questionFilterQuery->andWhere(['foquz_question_id' => $setting['question_id'], 'skipped' => 1]);
                    break;
            }
            if (!$i) {
                $filterAnswerID = $questionFilterQuery->column();
            } else {
                $filterAnswerID = array_intersect($filterAnswerID, $questionFilterQuery->column());
            }
            $i++;
        }
        if (!isset($questionFilterQuery)) {
            $filterAnswerID = self::find()->select('id')->where(['foquz_poll_id' => $poll_id])->column();
        }
        return $filterAnswerID;
    }

    public function behaviors()
    {
        return [
            [
                'class' => TimestampBehavior::class,
                'value' => (new \DateTime('now'))->format('Y-m-d H:i:s'),
            ],
            [
                'class' => HashIdBehavior::class,
            ],
        ];
    }

    /**
     * @return float|int|mixed|string|null
     * @deprecated
     */
    public function getElapsedTime()
    {
        $lastAnswerDateTime = $this->foquzAnswer ? max(ArrayHelper::getColumn($this->foquzAnswer, 'created_at')) : null;

        if (!$lastAnswerDateTime) {
            return 0;
        }

        if ($this->first_question_showed_at) {

            $timeDiff = DateTimeHelper::fromStringDateTime($lastAnswerDateTime)->getTimestamp() - DateTimeHelper::fromStringDateTime($this->first_question_showed_at)->getTimestamp();
            if ($this->foquzPoll->time_to_pass and $this->status == self::STATUS_IN_PROGRESS) {
                $elapsedTime = DateTimeHelper::timeStringToSeconds($this->foquzPoll->time_to_pass) + 1;
            } else {
                $elapsedTime = $timeDiff;
            }

            return $elapsedTime;
        }

        return 0;
    }

    public function getStatusId()
    {
        $values = [
            self::STATUS_NEW         => 0,
            self::STATUS_OPEN        => 1,
            self::STATUS_IN_PROGRESS => 2,
            self::STATUS_DONE        => 3,
        ];
        return $values[$this->status];
    }

    public function beforeSave($insert)
    {
        return parent::beforeSave($insert);
    }

    /**
     * Запускаем в очередь задачу на уведомления по которым нужен полный ответ.
     * Это задачи на новый ответ с полным ответом и задача на средняя оценка меньше указанного.
     * Ставим только при смене статусов. Если in-progress, то через 15 минут и она будет себя переставлять.
     * Письмо уходит всегда когда done и один раз если in-progress и 15 минут ничего не меняли,
     * то есть максимум и это должно быть редко два раза.
     * @return void
     */
    private function fullNotificationJob(): void
    {
        if (!empty(Yii::$app->rabbit_queue)) {
            $queue = Yii::$app->rabbit_queue;
            $queue
                ->delay($this->status == self::STATUS_DONE ? 0 : SiteNotification::DELAY_FULL_NOTIFICATION)
                ->push(new FullAnswerNotificationJob([
                    'answerId' => $this->id,
                    'status'   => $this->status,
                ]));
        }

        if (CustomQuotaService::isQuota($this->foquz_poll_id) && $this->status == self::STATUS_DONE && !empty($this->contact_id)) {
            $quota = new CustomQuotaService($this);
            $quota->counterQuota();
        }

        if (!empty(Yii::$app->queue_mailings_out)) {
            if ($this->status == self::STATUS_DONE && !empty($this->contact_id)) {
                $service = (new MailingsAfterPollService($this))->setQueue(Yii::$app->queue_mailings_out);
                if (isset(Yii::$app->redis)) {
                    $service->setCache(new MailingsAfterPollCache(Yii::$app->redis));
                }
                $service->sendMessages();
            }
        }


    }

    public function afterSave($insert, $changedAttributes)
    {
        if (!$this->isNewRecord && array_key_exists('status',
                $changedAttributes) && $this->status === self::STATUS_IN_PROGRESS) {
            DiscountPoolCode::updateAll(['answered' => date('Y-m-d')], ['answer_id' => $this->id]);
        }

        $changedStatus = !empty($changedAttributes) && in_array('status', array_keys($changedAttributes));

        if ($insert || $changedStatus) {
            $poll = $this->foquzPoll;
            if (!$this->status) {
                $this->status = self::STATUS_NEW;
            }
            switch ($this->status) {
                case self::STATUS_NEW:
                    $poll->updateCounters(['sent_answers_count' => 1]);
                    break;
                case self::STATUS_OPEN:
                    $poll->updateCounters(['opened_answers_count' => 1]);
                    if (!$insert && isset($changedAttributes['status']) && ($changedAttributes['status'] === self::STATUS_NEW || $changedAttributes['status'] === self::STATUS_EMAIL_OPEN)) {
                        $poll->updateCounters(['sent_answers_count' => -1]);
                    }
                    break;
                case self::STATUS_IN_PROGRESS:
                    $this->fullNotificationJob();
                    $poll->updateCounters(['opened_answers_count' => -1]);
                    $poll->updateCounters(['in_progress_answers_count' => 1]);
                    break;
                case self::STATUS_DONE:
                    $this->fullNotificationJob();
                    if (isset($changedAttributes['status']) && $changedAttributes['status'] === self::STATUS_IN_PROGRESS) {
                        $poll->updateCounters(['in_progress_answers_count' => -1]);
                    } elseif (isset($changedAttributes['status']) && $changedAttributes['status'] === self::STATUS_OPEN) {
                        $poll->updateCounters(['opened_answers_count' => -1]);
                    }
                    $poll->updateCounters(['filled_answers_count' => 1]);
                    break;
            }
            if (in_array($this->status, [self::STATUS_DONE, self::STATUS_IN_PROGRESS])) {
                Yii::$app->cache->delete(['main_answers_statuses', 'company_id' => $this->foquzPoll->company_id]);
            }
        }
        if (
            isset($changedAttributes['status']) &&
            $this->status !== $changedAttributes['status'] &&
            in_array($this->status, [self::STATUS_OPEN, self::STATUS_IN_PROGRESS, self::STATUS_DONE])
        ) {
            /** @var \yii\queue\amqp_interop\Queue $queue */
            $queue = Yii::$app->rabbit_queue;
            $queue
                ->push(new AnswerStatusJob([
                    'id'        => $this->id,
                    'status'    => $this->status,
                    'key'       => $this->sends[0]->key ?? null,
                    'widget_id' => $this->sends[0]->widget_id ?? null,
                ]));
        }
        if (
            isset($changedAttributes['status']) &&
            $this->status !== $changedAttributes['status'] &&
            $this->foquzPoll->isAnswerLimit !== (bool)$this->foquzPoll->is_answer_limited
        ) {
            $this->foquzPoll->is_answer_limited = $this->foquzPoll->isAnswerLimit;
            $this->foquzPoll->save();
        }
        parent::afterSave($insert, $changedAttributes);
    }

    public function beforeDelete()
    {
        $poll = $this->foquzPoll;
        switch ($this->status) {
            case self::STATUS_NEW:
                $poll->updateCounters(['sent_answers_count' => -1]);
                break;
            case self::STATUS_OPEN:
                $poll->updateCounters(['opened_answers_count' => -1]);
                break;
            case self::STATUS_IN_PROGRESS:
                $poll->updateCounters(['in_progress_answers_count' => -1]);
                break;
            case self::STATUS_DONE:
                $poll->updateCounters(['filled_answers_count' => -1]);
                break;
        }
        if ($processing = $this->processing) {
            switch ($processing->status) {
                case FoquzPollAnswerProcessing::STATUS_NEW:
                    $poll->updateCounters(['processing_new_answers_count' => -1]);
                    break;
                case FoquzPollAnswerProcessing::STATUS_IN_PROCESS:
                    $poll->updateCounters(['processing_inprocess_answers_count' => -1]);
                    break;
                case FoquzPollAnswerProcessing::STATUS_DELAYED:
                    $poll->updateCounters(['processing_delayed_answers_count' => -1]);
                    break;
                case FoquzPollAnswerProcessing::STATUS_WORK:
                    $poll->updateCounters(['processing_work_answers_count' => -1]);
                    break;
                case FoquzPollAnswerProcessing::STATUS_DONE:
                    $poll->updateCounters(['processing_done_answers_count' => -1]);
                    break;
            }
        }
        if (in_array($this->status, [self::STATUS_DONE, self::STATUS_IN_PROGRESS])) {
            Yii::$app->cache->delete(['main_answers_statuses', 'company_id' => $this->foquzPoll->company_id]);
        }
        /** @var Queue $queue */
        $queue = Yii::$app->rabbit_queue;
        $queue
            ->push(new AnswerStatusJob([
                'id'        => $this->id,
                'status'    => 'delete',
                'key'       => $this->sends[0]->key ?? null,
                'widget_id' => $this->sends[0]->widget_id ?? null,
            ]));
        self::saveDeletedAnswer($this->id);
        return parent::beforeDelete();
    }

    public static function saveDeletedAnswer(int $answerID): void
    {
        self::saveDeletedAnswers([$answerID]);
    }

    public static function saveDeletedAnswers(array $answersIDs): void
    {
        $answers = self::find()
            ->joinWith([
                'foquzAnswer.answerItemFiles',
                'foquzAnswer.hiddenQuestion',
                'complaint',
                'complaintFiles',
                'processing.processingFiles',
                'processing.answerNotifications',
                'sends',
            ])
            ->where(['foquz_poll_answer.id' => $answersIDs])
            ->orderBy(['foquz_poll_answer.id' => SORT_ASC])
            ->all();

        $insert = [];
        /** @var FoquzPollAnswer $answer */
        foreach ($answers as $answer) {
            $data = $answer->attributes;

            $data['foquzAnswer'] = [];
            foreach ($answer->foquzAnswer as $answerItem) {
                $answerData = $answerItem->attributes;
                $answerData['answerItemFiles'] = [];
                foreach ($answerItem->answerItemFiles as $file) {
                    $answerData['answerItemFiles'][] = $file->attributes;
                }
                $answerData['hiddenQuestion'] = $answerItem->hiddenQuestion->attributes ?? null;
                $data['foquzAnswer'][] = $answerData;
            }

            $data['complaint'] = $answer->complaint->attributes ?? null;
            $data['complaint']['foquzComplaintFiles'] = [];
            foreach ($answer->complaintFiles ?? [] as $file) {
                $data['complaint']['complaintFiles'][] = $file->attributes;
            }

            $data['processing'] = $answer->processing->attributes ?? null;
            $data['processing']['processingFiles'] = [];
            foreach ($answer->processing->processingFiles ?? [] as $file) {
                $data['processing']['processingFiles'][] = $file->attributes;
            }
            $data['processing']['AnswerNotifications'] = [];
            foreach ($answer->processing->answerNotifications ?? [] as $notification) {
                $data['processing']['AnswerNotifications'][] = $notification->attributes;
            }

            $data['sends'] = [];
            foreach ($answer->sends ?? [] as $send) {
                $data['sends'][] = $send->attributes;
            }
            $insert[] = [
                $answer->id,
                $answer->foquz_poll_id,
                $answer->created_at,
                $answer->status,
                date('Y-m-d H:i:s'),
                Yii::$app->user->id ?? null,
                Yii::$app->request->userIP ?? 'console',
                Json::encode($data),
            ];
        }

        Yii::$app->db->createCommand()->batchInsert('{{%foquz_poll_answer_deleted}}', [
            'answer_id',
            'foquz_poll_id',
            'answer_created_at',
            'status',
            'deleted_at',
            'deleted_by',
            'ip',
            'data',
        ], $insert)->execute();
    }

    public function afterDelete()
    {
        $this->foquzPoll->clearStatCache();
        parent::afterDelete();
    }

    /**
     * {@inheritdoc}
     */
    public function rules()
    {
        return [
            [['auth_key', 'foquz_poll_id'], 'required'],
            [['created_at', 'updated_at', 'order_id', 'custom_fields', 'first_question_showed_at', 'user_agreement'], 'safe'],
            [['client_id', 'foquz_poll_id', 'order_id', 'delivery_id', 'points', 'max_points', 'contact_id', 'processing_time_in_minutes',
                'language', /*'feedback_id', */'user_id', 'answer_time', 'quote_id'], 'integer'],
            [['auth_key', 'status', 'ip_address', 'device', 'points_job_id'], 'string', 'max' => 255],
            [['webhook_sent'], 'boolean'],
            [
                ['foquz_poll_id'],
                'exist',
                'skipOnError'     => true,
                'targetClass'     => FoquzPoll::class,
                'targetAttribute' => ['foquz_poll_id' => 'id']
            ],
            [
                ['client_id'],
                'exist',
                'skipOnError'     => true,
                'targetClass'     => Client::class,
                'targetAttribute' => ['client_id' => 'id']
            ],
            [
                ['user_id'],
                'exist',
                'skipOnError'     => true,
                'targetClass'     => User::class,
                'targetAttribute' => ['user_id' => 'id']
            ],
            [
                ['language'],
                function ($attribute) {
                    $langs = FoquzPollLang::find()->where(['foquz_poll_id' => $this->foquz_poll_id])->select('poll_lang_id')->column();
                    if (!in_array($this->$attribute, $langs)) {
                        $this->addError($attribute, 'Язык для опроса не найден');
                    }
                }
            ],
          //  [['feedback_id'], 'exist', 'targetRelation' => 'feedback'],
            [['quote_id'], 'exist', 'skipOnError' => true, 'targetClass' => FoquzPollLinkQuotes::class, 'targetAttribute' => ['quote_id' => 'id']],
        ];
    }

    /**
     * {@inheritdoc}
     */
    public function attributeLabels()
    {
        return [
            'id'            => 'ID',
            'created_at'    => 'Created At',
            'updated_at'    => 'Updated At',
            'client_id'     => 'Client ID',
            'user_id'       => 'ID пользователя-респондента',
            'auth_key'      => 'Auth Key',
            'foquz_poll_id' => 'Foquz Poll ID',
            'ip_address'    => 'Ip-адрес',
            'status'        => 'Status',
            'points'        => 'Points',
            'max_points'    => 'Максимальное количество баллов',
            'points_job_id' => 'ID задачи на подсчет баллов',
            'answer_time'   => 'Время ответа (в секундах)',
            'webhook_sent'  => 'Вебхук отправлен',
        ];
    }

    public function fields()
    {
        $fields = parent::fields();
        $fields[] = 'foquzAnswer';
        return $fields;
    }

    /**
     * @return \yii\db\ActiveQuery
     */
    public function getFoquzPoll()
    {
        return $this->hasOne(FoquzPoll::class, ['id' => 'foquz_poll_id']);
    }

    public function getPollLang()
    {
        return $this->hasOne(PollLang::class, ['id' => 'language']);
    }

    public function getMailingListSend()
    {
        return $this->hasMany(FoquzPollMailingListSend::class, ['answer_id' => 'id']);
    }

    public function getAnswerChannel()
    {
        return $this->hasOne(FoquzPollMailingListSend::class,
            ['answer_id' => 'id'])->where(['status' => FoquzPollMailingListSend::STATUS_OPEN]);
    }

    public function getSends()
    {
        return $this->hasMany(FoquzPollMailingListSend::className(), ['answer_id' => 'id']);
    }

    /**
     * @return \yii\db\ActiveQuery
     */
    public function getClient()
    {
        return $this->hasOne(Client::class, ['id' => 'client_id']);
    }

    /**
     * @return \yii\db\ActiveQuery
     */
    public function getUser()
    {
        return $this->hasOne(User::class, ['id' => 'user_id']);
    }

    public function getCompany()
    {
        return $this->hasOne(Company::class, ['id' => 'company_id'])->via('foquzPoll');
    }

    /**
     * @return \yii\db\ActiveQuery
     */
    public function getComplaint()
    {
        return $this->hasOne(FoquzComplaint::class, ['foquz_poll_answer_id' => 'id']);
    }

    /**
     * @return \yii\db\ActiveQuery
     */
    public function getComplaintFiles()
    {
        return $this->hasMany(FoquzComplaintFile::class, ['foquz_poll_answer_id' => 'id']);
    }

    /**
     * @return \yii\db\ActiveQuery
     */
    public function getPollLanguage()
    {
        return $this->hasOne(PollLang::class, ['id' => 'language']);
    }

    public function getClientEmail()
    {
        return $this->hasOne(ClientEmail::className(), ['client_id' => 'client_id']);
    }

    public function getFeedback()
    {
        return $this->hasOne(Feedback::class, ['id' => 'feedback_id']);
    }

    /**
     * @return \yii\db\ActiveQuery
     */
    public function getFoquzAnswer()
    {
        return $this->hasMany(FoquzPollAnswerItem::class, ['foquz_poll_answer_id' => 'id']);
    }

    public function getDisplayStatus()
    {
        return ArrayHelper::getValue(static::getStatuses(), $this->status);
    }

    public static function getStatuses()
    {
        return [
            self::STATUS_NEW         => 'Отправлена',
            self::STATUS_EMAIL_OPEN  => 'Письмо открыто',
            self::STATUS_OPEN        => 'Открыта',
            self::STATUS_IN_PROGRESS => 'В процессе',
            self::STATUS_DONE        => 'Заполнена',
        ];
    }

    /**
     * @return \yii\db\ActiveQuery
     */
    public function getOrder()
    {
        return $this->hasOne(Order::className(), ['id' => 'order_id']);
    }

    /**
     * @return \yii\db\ActiveQuery
     */
    public function getProcessing()
    {
        return $this->hasOne(FoquzPollAnswerProcessing::class,
            ['poll_answer_id' => 'id'])->orderBy(['foquz_poll_answer_processings.id' => SORT_DESC]);
    }

    public function getProcessings()
    {
        return $this->hasMany(FoquzPollAnswerProcessing::class, ['poll_answer_id' => 'id']);
    }

    public function getFoquzOrderType()
    {
        return $this->hasOne(FoquzOrderType::className(), ['id' => 'delivery_id']);
    }

    public function getContact()
    {
        return $this->hasOne(FoquzContact::className(), ['id' => 'contact_id']);
    }
    public function getAnswerTags(): ActiveQuery
    {
        return $this->hasMany(FoquzContactTag::class, ['answer_id' => 'id']);
    }

    public function getTags(): ActiveQuery
    {
        return $this->hasMany(FoquzCompanyTag::class, ['id' => 'tag_id'])->via('answerTags');
    }

    public function getShowedImage()
    {
        return $this->hasMany(FoquzPollAnswerShowedImage::class, ['foquz_poll_answer_id' => 'id']);
    }

    public function getVariables(): array
    {
        $needFio = FoquzQuestionIntermediateBlockSetting::find()
            ->joinWith('question', false)
            ->where(['foquz_question.poll_id' => $this->foquz_poll_id])
            ->andWhere([
                'OR',
                ['LIKE', 'foquz_question_intermediate_block_setting.text', '{ФИО}'],
                ['LIKE', 'foquz_question.description', '{ФИО}']
            ])
            ->exists();

//        print_r($this->contact && $needFio ? $this->contact->fullName : ''); exit;


        if ($this->answer_filial_id) {
            $filialParams = Filial::find()
                ->select(['param1', 'param2', 'param3', "name"])
                ->where(['company_id' => $this->foquzPoll->company_id, 'id' => $this->answer_filial_id])
                ->asArray()
                ->one();
        }

        $result = [
            'fio'                        => $this->contact && $needFio ? $this->contact->fullName : '',
            'order'                      => $this->order ? [
                'id'   => $this->order->id,
                'time' => $this->order->created_time,
            ] : null,
            'codes'                      => $this->foquzPoll->getIntermediateCodes($this),
            'scoresInterpretationRanges' => $this->foquzPoll->scoresInterpretationRanges,
            'FILIAL.param1'              => $filialParams['param1'] ?? '',
            'FILIAL.param2'              => $filialParams['param2'] ?? '',
            'FILIAL.param3'              => $filialParams['param3'] ?? '',
            'FILIAL.name'                => $filialParams['name'] ?? '',
        ];

        if (isset(Yii::$app->params['hr_manager_company_id']) && $this->foquzPoll->company_id == Yii::$app->params['hr_manager_company_id']) {
            $result = $this->getVariablesManagerHR($result);
        }
        return $result;
    }

    function getVariablesManagerHR(array $result = []): array
    {
        if ($this->answerFilial && $this->answerFilial->category) {
            $result['FILIAL.category_name'] = $this->answerFilial->category->name;
        }
        if ($this->contact) {
            $result['CONTACT.filial_name'] = implode(",", ArrayHelper::getColumn($this->contact->filials, "name"));
            $filialsId = ArrayHelper::getColumn($this->contact->filials, "id");
            $result['CONTACT.category_filial_name'] = implode(",",
                ArrayHelper::getColumn($this->contact->filials, "category.name"));

            $managerQuestions = FoquzQuestion::find()
                ->leftJoin(FoquzQuestionIntermediateBlockSetting::tableName(),
                    FoquzQuestionIntermediateBlockSetting::tableName() . ".question_id = " . FoquzQuestion::tableName() . ".id")
                ->where(['poll_id' => $this->foquz_poll_id])
                ->andWhere([
                    "OR",
                    ['LIKE', 'description', '{FILIAL_MANAGER'],
                    ['LIKE', 'description_html', '{FILIAL_MANAGER'],
                    ['LIKE', 'sub_description', '{FILIAL_MANAGER'],
                    ['LIKE', 'description', '{MANAGER'],
                    ['LIKE', 'description_html', '{MANAGER'],
                    ['LIKE', 'sub_description', '{MANAGER'],
                    ['LIKE', FoquzQuestionIntermediateBlockSetting::tableName() . '.text', '{FILIAL_MANAGER'],
                    ['LIKE', FoquzQuestionIntermediateBlockSetting::tableName() . '.text', '{MANAGER']
                ]);
            $managerQuestions = $managerQuestions->all();
            $rows = [];
            foreach ($managerQuestions as $q) {
                $rows[] = $q->description;
                $rows[] = $q->description_html;
                $rows[] = $q->sub_description;
                $rows[] = $q->intermediateBlock->text;
            }

            foreach ($rows as $row) {
                if ($this->answer_filial_id && preg_match_all("@\{(FILIAL_MANAGER\..*)}@", $row, $matches)) {
                    foreach ($matches[1] as $match) {
                        if (isset($result[$match])) {
                            continue;
                        }
                        if (!preg_match_all("@FILIAL_MANAGER.(\d+)_(\d+)_answer@", $match, $paramsVar)) {
                            continue;
                        }
                        $varPollId = $paramsVar[1][0];
                        $varQuestionId = $paramsVar[2][0];
                        $hrManagerService = new HRManagerService($this->contact);
                        $result[$match] = $hrManagerService->getAnswerFilial($varPollId, $varQuestionId,
                            $this->answer_filial_id);
                    }
                }
                if (preg_match_all("@\{(MANAGER\..*)}@", $row, $matches)) {
                    foreach ($matches[1] as $match) {
                        if (isset($result[$match])) {
                            continue;
                        }
                        if (!preg_match_all("@MANAGER.(\d+)_(\d+)_answer@", $match, $paramsVar)) {
                            continue;
                        }
                        $varPollId = $paramsVar[1][0];
                        $varQuestionId = $paramsVar[2][0];
                        $hrManagerService = new HRManagerService($this->contact);
                        $result[$match] = $hrManagerService->getAnswer($varPollId, $varQuestionId);
                    }
                }
            }
        }

        return $result;
    }

    /**
     * {@inheritdoc}
     */
    public static function tableName()
    {
        return '{{%foquz_poll_answer}}';
    }

    public function getAnswerFilial()
    {
        return $this->hasOne(Filial::className(), ['id' => 'answer_filial_id']);
    }

    public function isExpirated(): bool
    {
        $expirationInMinutes = $this->foquzPoll->expiration_in_minutes ?? 0;

        if ($expirationInMinutes === null || $expirationInMinutes === 0) {
            return false;
        }

        $expiratedInTimestamp = (new \DateTime($this->created_at))->getTimestamp() + $expirationInMinutes * 60;
        $nowTimestamp = (new \DateTime(null))->getTimestamp();

        return $nowTimestamp > $expiratedInTimestamp;
    }

    public function isCorrect($limits)
    {
        $ratingFilter = null;
        if (isset($_GET['ratingFilter']) && isset($_GET['table'])) {
            $ratingFilter = array_filter(explode(' ', $_GET['ratingFilter']));
        }
        $items = $this->foquzAnswer;
        $not_found = true;
        if ($limits) {
            foreach ($limits as $key => $limit) {
                if ($limit[0] == 1 && $limit[1] == 5) {
                    unset($limits[$key]);
                }
            }
        }
        foreach ($items as $key => $item) {
            $name = str_replace(' ', '_', $item->foquzQuestion->service_name);

            $rating = $item->getAverageRating();
            if (isset($limits[$name])) {
                $low = $limits[$name][0];
                $high = $limits[$name][1];
                $not_found = false;
                if ($rating < $low || $rating > $high) {
                    return false;
                }
                unset($limits[$name]);
            }
            if ($ratingFilter && in_array($rating . "", $ratingFilter)) {
                $ratingFilter = array_diff($ratingFilter, [$rating . ""]);
            }
        }
        if ($ratingFilter && count($ratingFilter)) {
            return false;
        }
        //print_r($limits);
        return !$limits || count($limits) == 0;
    }

    public function getRatedAnswers()
    {
        $answers = $this->foquzAnswer;
        foreach ($answers as $key => $answer) {
            if ($answer->foquzQuestion->rating_type != 1) {
                unset($answers[$key]);
            }
        }
        return array_values($answers);
    }

    public function collectNotifications()
    {
        $channels = Channel::find()->where(['poll_id' => $this->foquz_poll_id])->orderBy(['position' => SORT_ASC])->all();
        $result = [];
        foreach ($channels as $channel) {
            $result[] = [
                'channelName' => $channel->name,
                'active'      => $channel->active,
                'repeatCount' => $channel->getRepeatsCount(),
                'className'   => strtolower($channel->name),
            ];
        }
        return $result;
    }

    public function collectAnswers()
    {
        $answers = [];
        $this->refresh();
        foreach ($this->foquzAnswer as $item) {
            if ($item->foquzQuestion->service_name == 'Товар' && $item->foquzQuestion->is_system) {
                $n1 = 0;
                $s = 0;
                $scores = [];
                foreach ($this->foquzPollDishes as $dish) {
                    $n1++;
                    $s += intval($dish->score);
                }
                $answers[] = [
                    'question' => (object)['name' => $item->foquzQuestion->service_name],
                    'average'  => 3,
                    'rating'   => round($s / $n1),
                    'type'     => 1,
                    'answered' => true,
                    'comment'  => $item->foquzQuestion->main_question_type === FoquzQuestion::TYPE_ASSESSMENT ? ($item->self_variant ?? $item->answer) : null,
                ];
            } else {
                $answers[] = [
                    'question' => (object)['name' => $item->foquzQuestion->service_name],
                    'average'  => 1,
                    'rating'   => $item->rating,
                    'type'     => $item->foquzQuestion->rating_type,
                    'answered' => true,
                    'comment'  => $item->foquzQuestion->main_question_type === FoquzQuestion::TYPE_ASSESSMENT ? ($item->self_variant ?? $item->answer) : null,
                    'selected' => is_array($item->detail_item) ? $item->detail_item : json_decode($item->detail_item),
                ];
            }
        }
        return $answers;
    }

    function collectQuestions()
    {
        $questions = [];
        $key = 0;
        $result = null;
        foreach ($this->foquzPoll->foquzQuestions as $question) {
            $questions[$key] = [
                'id'                         => $question->id,
                'name'                       => $question->service_name,
                'assessmentType'             => $question->rating_type,
                'questionName'               => $question->name,
                'shortName'                  => function ($question) {
                    if ($question->service_name != '') {
                        return $question->service_name;
                    }
                    if ($question->name != '') {
                        return $question->name;
                    }
                    return $question->description;
                },
                'mediaType'                  => $question->type == 'text' ? 0 : ($question->type == 'image' ? 1 : 2),
                'text'                       => $question->description,
                'is_self_answer'             => $question->is_self_answer,
                'clarifyingQuestion'         => $question->detail_question,
                'clarifyingQuestionVariants' => $question->collectClarifyingQuestionVariants(),
                'variants'                   => $question->collectVariants(),
                'dishes'                     => $question->service_name == 'Товар' && $question->is_system,
                'employee'                   => [
                    'name' => null,
                ],
            ];
            $answer_item = FoquzPollAnswerItem::findOne([
                'foquz_question_id'    => $question->id,
                'foquz_poll_answer_id' => $this->id
            ]);
            if ($answer_item) {
                if ($answer_item->foquzQuestion->main_question_type === FoquzQuestion::TYPE_DATE) {
                    $decoded_date = json_decode($answer_item->answer);
                    if ($decoded_date !== null) {
                        $questions[$key]['answer'] = ($decoded_date->date ? $decoded_date->date : '') . ' ' . (str_replace(' ',
                                '', $decoded_date->time) ? str_replace(' ', '', $decoded_date->time) : '');
                    } else {
                        $questions[$key]['answer'] = '';
                    }
                } elseif ($answer_item->foquzQuestion->main_question_type === FoquzQuestion::TYPE_VARIANTS) {
                    $selected = is_array($answer_item->detail_item) ? $answer_item->detail_item : json_decode($answer_item->detail_item);
                    $result['selectedVariantIndex'] = $selected;
                    $result['clarifyingQuestionCustomAnswer'] = $answer_item->self_variant != "" ? $answer_item->self_variant : null;
                    $result['comment'] = $answer_item->self_variant != "" ? $answer_item->self_variant : null;
                    $questions[$key]['answer'] = (object)$result;
                } elseif (in_array($answer_item->foquzQuestion->main_question_type,
                    [FoquzQuestion::TYPE_TEXT_ANSWER, FoquzQuestion::TYPE_ADDRESS])) {
                    $questions[$key]['answer'] = $answer_item->answer != "" ? $answer_item->answer : null;
                } elseif ($answer_item->foquzQuestion->main_question_type === FoquzQuestion::TYPE_FILE_UPLOAD) {
                    $result = [
                        'files'    => ArrayHelper::getColumn($answer_item->getAnswerItemFiles()->select('file_path')->all(),
                            "file_path"),
                        'passedAt' => date('d.m.Y', strtotime($answer_item->created_at)),
                        'comment'  => $answer_item->answer != "" ? $answer_item->answer : null,
                    ];
                    $questions[$key]['answer'] = (object)$result;
                } elseif ($answer_item->foquzQuestion->main_question_type === FoquzQuestion::TYPE_FORM) {
                    $values = [];
                    $dbAnswer = json_decode($answer_item->answer);
                    $properties = $answer_item->foquzQuestion->getFormFields()->select('id, name, link_with_client_field, linked_client_field')->all();
                    foreach ($properties as $property) {
                        $id = $property->id;
                        if (isset($dbAnswer->$id) && $dbAnswer->$id !== '') {
                            $values[] = [
                                'label'               => $property->name,
                                'value'               => $dbAnswer->$id,
                                'linkWithClientField' => $property->link_with_client_field,
                                'linkedClientField'   => ContactAdditionalField::getText($property->linked_client_field),
                            ];
                        }
                    }
                    $result['values'] = $values;
                    $questions[$key]['answer'] = (object)$result;
                } else {
                    $clarifyingQuestionCustomAnswer = $answer_item->self_variant != '' ? $answer_item->self_variant : null;
                    if ($answer_item->is_self_variant) {
                        $result = [
                            "rating"                                 => $answer_item->rating,
                            "clarifyingQuestionSelectedVariantIndex" => null,
                            "clarifyingQuestionCustomAnswer"         => $clarifyingQuestionCustomAnswer,
                            'comment'                                => $answer_item->answer != "" ? $answer_item->answer : null,
                        ];
                    } else {
                        if ($answer_item->detail_item != "" && isset($question_array['clarifyingQuestionVariants'])) {
                            $asnwer_item_id = intval(preg_replace('/[^0-9]/', '', ($answer_item->detail_item[0])));

                            $question_detail = FoquzQuestionDetail::findOne(['id' => $asnwer_item_id]);
                            if ($question_detail) {
                                $index = array_search($question_detail->question,
                                    $questions[$key]['clarifyingQuestionVariants']);
                                $result = [
                                    "rating"                                 => $answer_item->rating,
                                    "selectedVariantIndex"                   => $index,
                                    "clarifyingQuestionSelectedVariantIndex" => $index,
                                    "clarifyingQuestionCustomAnswer"         => $clarifyingQuestionCustomAnswer
                                ];
                            }
                        } else {
                            $result = [
                                "rating"                                 => $answer_item->rating,
                                "clarifyingQuestionSelectedVariantIndex" => 0,
                                "clarifyingQuestionCustomAnswer"         => $clarifyingQuestionCustomAnswer,
                                'comment'                                => $answer_item->answer != "" ? $answer_item->answer : null,
                            ];
                        }
                    }
                    if (!$result['rating']) {
                        $result['rating'] = $answer_item->rating;
                    }
                    if ($answer_item->detail_item != "") {
                        $selected = is_array($answer_item->detail_item) ? $answer_item->detail_item : json_decode($answer_item->detail_item);
                        $result['selectedVariantIndex'] = $selected;
                        $result['clarifyingQuestionCustomAnswer'] = $clarifyingQuestionCustomAnswer;
                        $result['comment'] = $answer_item->answer != "" ? $answer_item->answer : null;
                    }
                    if ($questions[$key]['dishes'] === true) {
                        foreach ($this->getFoquzPollDishes()->orderBy('score desc')->all() as $dishScore) {
                            if ($this->order) {
                                $dishOrderData = $this->order->getDishes()->where(['dish_id' => $dishScore->dish_id])->one();
                                $result['dishRatings'][] = [
                                    'dish'     => ['name' => $dishScore->dish->name],
                                    'quantity' => $dishOrderData->quantity ?? 0,
                                    'value'    => $dishScore->score,
                                ];
                            }
                        }
                        $result['comment'] = $answer_item->answer != "" ? $answer_item->answer : null;
                    }
                    if ($answer_item->foquzQuestion->is_system) {
                        if ($answer_item->foquzQuestion->service_name === 'Оператор') {
                            $result['employee']['name'] = $this->order ? $this->order->operator->name : '';
                        } elseif ($answer_item->foquzQuestion->service_name === 'Курьер') {
                            $result['employee']['name'] = $this->order ? $this->order->driver->name : '';
                        }
                    }
                    $questions[$key]['answer'] = (object)$result;
                }
            } else {
                $questions[$key]['answer'] = null;
            }
            if ($question->type == 'image') {
                $questions[$key]['imageUrls'] = ArrayHelper::getColumn($question->questionFiles, function ($element) {
                    return '/' . $element->file_path;
                });
            }
            if ($question->type == 'video') {
                $questions[$key]['videoUrls'] = ArrayHelper::getColumn($question->getQuestionFiles()->where(['type' => 'video'])->all(),
                    function ($element) {
                        return '/' . $element->file_path;
                    });
            }
            $key++;
        }
        return $questions;
    }

    /**
     * @return \yii\db\ActiveQuery
     */
    public function getFoquzPollDishes()
    {
        return $this->hasMany(FoquzPollDishScore::class, ['answer_id' => 'id']);
    }

    public function linkForPoll()
    {
        $answeredListSend = FoquzPollMailingListSend::find()
            ->where([
                'answer_id' => $this->id,
            ])->orderBy('id DESC')->one();
        if ($answeredListSend) {
            return [
                Yii::$app->params['protocol'] . '://' . $this->foquzPoll->company->alias . '/p/' . $answeredListSend->key,
                $answeredListSend->id
            ];
        }
        return ['', null];
    }

    public function getSendChannelName()
    {
        foreach ($this->sends as $send) {
            if ($send->status == FoquzPollMailingListSend::STATUS_OPEN) {
                return $send->channel_name;
            }
        }
        return '';
    }

    public function getAnswerKey()
    {
        foreach ($this->sends as $send) {
            if ($send->status == FoquzPollMailingListSend::STATUS_OPEN) {
                return $send->key;
            }
        }
        return $this->auth_key;
    }

    public function addPoints()
    {
        $points = FoquzPollAnswerItem::find()->where(["foquz_poll_answer_id" => $this->id])->select("SUM(points)")->scalar();
        if (!$points) {
            $points = 0;
        }
        return $points;
        $points = $this->points ?? 0;
        return $points += self::calculatePointsForQuestion($question, $answerItem);
    }

    /**
     * Рассчитывает количество баллов, которое получит пользователь за ответ на вопрос
     * @param FoquzQuestion $question
     * @param FoquzPollAnswerItem $answerItem
     * @return int|mixed|null
     */
    public static function calculatePointsForQuestion(FoquzQuestion $question, $answerItem)
    {
        $points = 0;
        switch ($question->main_question_type) {
            case FoquzQuestion::TYPE_VARIANTS:
                $userAnswer = json_decode($answerItem->detail_item);
                if (is_array($userAnswer)) {
                    foreach ($userAnswer as $detailId) {
                        if ($question->donor && $question->donor !== $question->id) {
                            if ($detailId == '-1') {
                                $detail = RecipientQuestionDetail::findOne([
                                    'recipient_id'       => $question->id,
                                    'question_detail_id' => null
                                ]);
                            } elseif ($question->donor && $question->getMainDonor()->main_question_type !== FoquzQuestion::TYPE_DICTIONARY) {
                                $detail = RecipientQuestionDetail::findOne([
                                    'recipient_id'       => $question->id,
                                    'question_detail_id' => $detailId
                                ]);
                            } else {
                                $detail = RecipientQuestionDetail::findOne([
                                    'recipient_id'          => $question->id,
                                    'dictionary_element_id' => $detailId
                                ]);
                            }
                        } else {
                            $detail = FoquzQuestionDetail::findOne($detailId);
                        }
                        if ($detail && $detail->points && !$detail->without_points) {
                            $points += $detail->points;
                        }
                    }
                }
                break;
            case FoquzQuestion::TYPE_DATE:
                $userAnswer = json_decode($answerItem->answer);
                if (
                    $question->rightAnswer !== null && $userAnswer !== null
                ) {
                    $dateRight = date('Y-m-d',
                            strtotime(trim($userAnswer->date))) === $question->rightAnswer->decodedAnswer->date || $question->rightAnswer->decodedAnswer->date === '' || $userAnswer->date === $question->rightAnswer->decodedAnswer->date || preg_replace('@^(\d+\.)(\d)$@',
                            '${1}0$2', $userAnswer->date) === $question->rightAnswer->decodedAnswer->date;
                    $timeRight = str_replace(' ', '', $userAnswer->time) === str_replace(' ', '',
                            $question->rightAnswer->decodedAnswer->time) || str_replace(' ', '',
                            $question->rightAnswer->decodedAnswer->time) === '__:__' || $userAnswer->time === $question->rightAnswer->decodedAnswer->time;
                    if ($dateRight && $timeRight) {
                        $points += $question->rightAnswer->points;
                    } else {
                        $userAnswerText = isset($userAnswer->date) ? trim($userAnswer->date) : "";
                        $userAnswerText .= isset($userAnswer->time) ? trim($userAnswer->time) : "";
                        $rightAnswerText = isset($question->rightAnswer->decodedAnswer->date) ? trim($question->rightAnswer->decodedAnswer->date) : "";
                        $rightAnswerText .= isset($question->rightAnswer->decodedAnswer->time) ? trim($question->rightAnswer->decodedAnswer->time) : "";
                        if ($userAnswerText == $rightAnswerText) {
                            $points += $question->rightAnswer->points;
                        }
                    }
                }
                break;
            case FoquzQuestion::TYPE_PRIORITY:
                if ($answerItem->answer) {
                    $userAnswer = $answerItem->answer;
                } elseif ($answerItem->detail_item) {
                    $userAnswer = [];
                    foreach ($answerItem->detail_item as $detail) {
                        if ($detail == -1 && $question->donor) {
                            $userAnswer[] = $question->getMainDonor()->self_variant_text;
                        } else {
                            if ((!$question->donor || $question->getMainDonor()->main_question_type !== FoquzQuestion::TYPE_DICTIONARY) &&
                                $detailItem = FoquzQuestionDetail::findOne($detail)
                            ) {
                                $userAnswer[] = $detailItem->question;
                            } elseif ($question->donor && $detailItem = DictionaryElement::findOne($detail)) {
                                $userAnswer[] = $detailItem->fullPath;
                            }
                        }
                    }
                }
                if ($question->rightAnswer && $userAnswer === $question->rightAnswer->decodedAnswer) {
                    $points += $question->rightAnswer->points;
                }
                break;
            case FoquzQuestion::TYPE_CHOOSE_MEDIA:
                $userAnswer = json_decode($answerItem->answer);
                if (is_array($userAnswer)) {
                    foreach ($userAnswer as $fileId) {
                        $file = FoquzQuestionFile::findOne($fileId);
                        $points += ($file->points ?? 0);
                    }
                }
                break;
            case FoquzQuestion::TYPE_SIMPLE_MATRIX:
                $matrixSettings = json_decode($question->matrix_settings);
                $userAnswer = json_decode($answerItem->answer);
                if ($userAnswer) {
                    foreach ($userAnswer as $r => $c) {
                        if ($question->donor && $question->donor !== $question->id) {
                            if ($r == '-1') {
                                $r = $question->getMainDonor()->self_variant_text;
                            } elseif ($question->getMainDonor()->main_question_type === FoquzQuestion::TYPE_DICTIONARY) {
                                $r = DictionaryElement::findOne($r)->fullPath ?? null;
                            } else {
                                $detailItem = FoquzQuestionDetail::findOne($r);
                                if ($detailItem) {
                                    $r = $detailItem->question;
                                }
                            }
                        }
                        if (is_array($c)) {
                            foreach ($c as $item) {
                                if ($item === '-1') {
                                    continue;
                                }
                                $point = $matrixSettings->points[array_search($r, $matrixSettings->rows)][array_search($item, $matrixSettings->cols)] ?? 0;
                                if (is_numeric($point)) {
                                    $points += $point;
                                }
                            }
                        }
                    }
                }
                break;
        }
        return $points;
    }

    public function calculateMaxPoints()
    {
        if (!$this->foquzPoll->point_system) {
            return null;
        }
        $questionsIDsWithAnswers = FoquzPollAnswerItem::find()
            ->select('foquz_question_id')
            ->where(['foquz_poll_answer_id' => $this->id])
            ->column();
        $needDynamicCalculation = FoquzPollAnswerItem::find()
            ->leftJoin('foquz_question', 'foquz_question.id = foquz_poll_answer_item.foquz_question_id')
            ->leftJoin('foquz_question_detail', 'foquz_question_detail.foquz_question_id = foquz_question.id')
            ->where([
                'foquz_poll_answer_id'                 => $this->id,
                'foquz_question.main_question_type'    => FoquzQuestion::TYPE_VARIANTS,
                'foquz_question_detail.is_deleted'     => 0,
                'foquz_question_detail.without_points' => true,
            ])
            ->exists();
        if (!$needDynamicCalculation) {
            $needDynamicCalculation = FoquzQuestion::find()
                ->where(['poll_id' => $this->foquz_poll_id,])
                ->andWhere([
                    'OR',
                    ['max_points_calc_method' => FoquzQuestion::MAX_POINTS_CALC_METHOD_WITHOUT_HIDDEN],
                    ['max_points_calc_recipient' => 1],
                ])
                ->andWhere(['OR', ['is_deleted' => 0], ['id' => $questionsIDsWithAnswers]])
                ->exists();
        }
        if (!$needDynamicCalculation) {
            return $this->foquzPoll->max_points;
        }
        $questionsWithPoint = FoquzQuestion::find()
            ->where(['poll_id' => $this->foquz_poll_id])
            ->andWhere([
                'main_question_type' => [
                    FoquzQuestion::TYPE_DATE,
                    FoquzQuestion::TYPE_VARIANTS,
                    FoquzQuestion::TYPE_PRIORITY,
                    FoquzQuestion::TYPE_CHOOSE_MEDIA,
                    FoquzQuestion::TYPE_SIMPLE_MATRIX,
                ]
            ])
            ->andWhere(['OR', ['is_deleted' => 0], ['id' => $questionsIDsWithAnswers]])
            ->all();
        $max = 0;
        $hiddenQuestions = FoquzPollAnswerItem::find()
            ->select('foquz_question.id')
            ->joinWith(['hiddenQuestion.question'])
            ->where([
                'foquz_poll_answer_id'                  => $this->id,
                'foquz_question.max_points_calc_method' => FoquzQuestion::MAX_POINTS_CALC_METHOD_WITHOUT_HIDDEN,
            ])
            ->andWhere(['NOT', ['foquz_poll_answer_hidden_question.id' => null]])
            ->column();

        $items = FoquzPollAnswerItem::find()
            ->leftJoin('foquz_question', 'foquz_question.id = foquz_poll_answer_item.foquz_question_id')
            ->where([
                'foquz_poll_answer_id'              => $this->id,
                'foquz_question.main_question_type' => FoquzQuestion::TYPE_SIMPLE_MATRIX,
            ])
            ->all();
        $items = ArrayHelper::index($items, 'foquz_question_id');

        /** @var FoquzQuestion $question */
        foreach ($questionsWithPoint as $question) {
            if (in_array($question->id, $hiddenQuestions)) {
                continue;
            }
            if (
                $question->main_question_type !== FoquzQuestion::TYPE_SIMPLE_MATRIX ||
                !$question->donor ||
                !$question->max_points_calc_recipient
            ) {
                $max += $question->maxPoints;
            } else {
                /** @var FoquzPollAnswerItem $item */
                $item = $items[$question->id] ?? null;

                if (!$item) {
                    continue;
                }

                $max += $item->calculateMaxPoints();
            }
        }

        /** @var FoquzPollAnswerItem[] $variantsAnswers */
        $variantsAnswers = FoquzPollAnswerItem::find()
            ->joinWith('foquzQuestion')
            ->where([
                'foquz_poll_answer_id'              => $this->id,
                'foquz_question.main_question_type' => FoquzQuestion::TYPE_VARIANTS,
            ])
            ->all();
        $detailsWithoutPoints = FoquzQuestionDetail::find()
            ->select('id')
            ->where(['foquz_question_id' => ArrayHelper::getColumn($variantsAnswers, 'foquz_question_id')])
            ->andWhere(['without_points' => true])
            ->column();

        foreach ($variantsAnswers as $answer) {
            $answerDetails = $answer->detail_item;
            if (is_string($answerDetails)) {
                $answerDetails = json_decode($answerDetails) ?? [];
            }
            if (!empty($answerDetails) && count(array_diff($answerDetails, $detailsWithoutPoints)) === 0) {
                $max -= $answer->foquzQuestion->maxPoints;
            }
        }
        return $max;
    }

    public function getByApi()
    {
        return ApiSendPoll::find()
                ->where(['answer_id' => $this->id])
                ->one() !== null;
    }

    /**
     * Отправка уведомлений на почту о новом ответе без самого ответа.
     * Уходит после ответа на первый вопрос.
     * @return void
     */
    public function sendEmailNotification(): void
    {
        $service = new NotificationsPollService($this);
        $service->sendNotification(NotificationType::ANSWER_NEW,
            NotificationChannel::EMAIL, false);
    }

    /**
     * Отправка PUSH уведомления о новом ответе без самого ответа.
     * Уходит после ответа на первый вопрос.
     * @return void
     */
    public function sendPushNotification(): void
    {
        $service = new NotificationsPollService($this);
        $service->sendNotification(NotificationType::ANSWER_NEW,
            NotificationChannel::PUSH);
    }

    public function getDeviceText()
    {
        switch ($this->device) {
            case 'desktop':
                return 'Десктоп';
                break;
            case 'tablet':
                return 'Планшет';
                break;
            case 'mobile':
                return 'Смартфон';
                break;
            default:
                return null;
        }
    }

    public function getLastQuestionId()
    {
        $lastQuestionId = $this
            ->foquzPoll
            ->getFoquzQuestions()
            ->select('foquz_question.id')
            ->joinWith('intermediateBlock')
            //->where(['is not', 'name', NULL])
            ->where(['is_tmp' => false])
            ->andWhere(['is_deleted' => false])
            ->andWhere([
                'NOT',
                [
                    'AND',
                    ['main_question_type' => FoquzQuestion::TYPE_INTERMEDIATE_BLOCK],
                    ['foquz_question_intermediate_block_setting.screen_type' => FoquzQuestionIntermediateBlockSetting::SCREEN_TYPE_END],
                ]
            ])
            ->orderBy(['position' => SORT_DESC])
            ->scalar();

        if ($this->foquzPoll->is_auto && $this->order) {
            $pollQuestions = [];
            $pollQuestionsAll = $this->foquzPoll->getFoquzQuestions()->andWhere(['is_tmp' => false])->andWhere([
                '<>',
                'service_name',
                ''
            ])
                ->with([
                    "pointSelected" => function ($q) {
                        $q->with("condition");
                    }
                ])
                ->orderBy(['position' => SORT_DESC])
                ->all();
            foreach ($pollQuestionsAll as $q) {
                $find = count($q->pointSelected) === 0;
                foreach ($q->pointSelected as $point) {
                    if (!$point->condition) {
                        $find = true;
                    } else {
                        $find = true;
                        if (($point->condition->order_type_id && $point->condition->order_type_id != $this->order->delivery_type) || ($point->condition->order_source && $point->condition->order_source != $this->order->source_type)) {
                            $find = false;
                        }
                    }
                    if ($find) {
                        break;
                    }
                }
                if ($find) {
                    $pollQuestions[] = $q;
                }
            }
            $lastQuestionId = $pollQuestions[0]->id;
        }

        return $lastQuestionId;
    }

    public function linkWithClientIfPossible($currentQuestion, $answer): bool
    {
        $contact = null;
        $doLink = false;
        $phone = null;
        $email = null;
        $companyId = $this->foquzPoll->company_id;
        if ($currentQuestion->main_question_type !== FoquzQuestion::TYPE_FORM && $currentQuestion->link_with_client_field) {
            $linked_client_field = $currentQuestion->linked_client_field;
            if ($currentQuestion->linked_client_field == 'phone' && $currentQuestion->mask == 1) {
                if ($answer['answer'] ?? null) {
                    $phone = $answer['answer'];
                    $doLink = $phone;
                    $phone = $phone ? FoquzContact::preformatPhone($phone) : null;
                    $contact = FoquzContact::findOne([
                        'phone'      => $phone,
                        'company_id' => $companyId,
                        'is_deleted' => false
                    ]);
                }
            } elseif ($currentQuestion->linked_client_field == 'email' && $currentQuestion->mask == 2) {
                if ($answer['answer'] ?? null) {
                    $email = $answer['answer'];
                    $doLink = true;
                    $contact = FoquzContact::findOne([
                        'email'      => $email,
                        'company_id' => $companyId,
                        'is_deleted' => false
                    ]);
                }
            }
        } elseif ($currentQuestion->main_question_type === FoquzQuestion::TYPE_FORM && ($quizzes = FoquzQuestionFormField::findAll([
                'question_id'            => $currentQuestion->id,
                'link_with_client_field' => 1
            ]))) {
            foreach ($quizzes as $item) {
                if ($item->linked_client_field == 'phone' && $item->mask_type == 1) {
                    $phone = $answer[$item->id];
                    $linked_client_field = 'phone';
                    $doLink = true;
                } elseif ($item->linked_client_field == 'email' && $item->mask_type == 2) {
                    $email = @$answer[$item->id];
                    $linked_client_field = 'email';
                    $doLink = true;
                }
            }

            $phone = $phone ? FoquzContact::preformatPhone($phone) : null;
            if ($phone and $email) {
                $linked_client_field = 'phone';
                $contact = FoquzContact::findOne([
                    'phone'      => $phone,
                    'company_id' => $companyId,
                    'is_deleted' => false
                ]);
                if (!$contact) {
                    $contact = FoquzContact::findOne([
                        'email'      => $email,
                        'company_id' => $companyId,
                        'is_deleted' => false
                    ]);
                }
                $doLink = true;
            } elseif ($phone) {
                $linked_client_field = 'phone';
                $contact = FoquzContact::findOne([
                    'phone'      => $phone,
                    'company_id' => $companyId,
                    'is_deleted' => false
                ]);
                $doLink = true;
            } elseif ($email) {
                $linked_client_field = 'email';
                $contact = FoquzContact::findOne([
                    'email'      => $email,
                    'company_id' => $companyId,
                    'is_deleted' => false
                ]);
                $doLink = true;
            }
        }

        if (empty($phone) && empty($email)) {
            return false;
        }

        if ($doLink) {

            if (!$contact) {
                $contact = new FoquzContact();
                $contact->{$linked_client_field} = $phone ?? $email;
                $contact->company_id = $this->foquzPoll->company_id;
            }
            if (!$contact->save()) {
                Yii::error($contact->errors);
            }
            $this->contact_id = $contact->id;
            if (!$this->save()) {
                Yii::error($this->errors);
            }
            $this->refresh();

            return true;
        }

        return false;
    }

    public function saveQuestionAnswer(FoquzQuestion $currentQuestion, $lastQuestionId, $params, $staffEditType = 0)
    {
        $answerForm = null;
        if (isset($params['lang']) && $params['lang']) {
            $this->language = $params['lang'];
            $this->save();
            unset($params['lang']);
        }

        if ($currentQuestion->main_question_type === FoquzQuestion::TYPE_INTERMEDIATE_BLOCK) {
            if ((bool)$currentQuestion->intermediateBlock->agreement) {
                $this->user_agreement = true;
                $this->save();
            }

            $pointPercent = $this->max_points > 0 ? round(($this->points / $this->max_points) * 100) : 0;
            if ($pointPercent < 0) {
                $pointPercent = 0;
            }

            return [
                $answerForm,
                [
                    'lastQuestion' => (int)$lastQuestionId === (int)$currentQuestion->id,
                    'points'       => $this->foquzPoll->point_system ? [
                        'points'  => $this->points,
                        'max'     => $this->max_points,
                        'percent' => $pointPercent,
                    ] : null
                ]
            ];
        }

        if (is_null($this->processing)) {
            $this->addProcessingIfNeeded();
            $this->foquzPoll->updateCounters(['processing_new_answers_count' => 1]);
        }

        if ($currentQuestion->main_question_type === FoquzQuestion::TYPE_DATE) {
            foreach ($params as $k => $v) {
                if ($v === '') {
                    unset($params[$k]);
                }
            }
        }

        $newRecord = false;
        $answerForm = FoquzPollAnswerItem::findOne([
            'foquz_poll_answer_id' => $this->id,
            'foquz_question_id'    => $currentQuestion->id,
        ]);

        if (!$answerForm) {
            $answerForm = new FoquzPollAnswerItem([
                'rating'               => null,
                'foquz_poll_answer_id' => $this->id,
                'foquz_question_id'    => $currentQuestion->id,
            ]);
            $newRecord = true;
        }

        if ($currentQuestion->main_question_type === FoquzQuestion::TYPE_FIRST_CLICK) {
            if ($newRecord) {
                if (isset($params['time_start'])) {
                    $answerForm->detail_item = ['time_start' => time()];
                    $lastQuestionId = null;
                }
            }
        } else {
            $answerForm->detail_item = null;
        }

        $answerForm->answer = null;
        $answerForm->rating = null;
        $answerForm->is_self_variant = 0;
        $answerForm->self_variant = null;
        $answerForm->question_name = $currentQuestion->service_name;
        $answerForm->skipped = $params['skipped'] ?? 0;

        if ($currentQuestion->main_question_type === FoquzQuestion::TYPE_FILE_UPLOAD && empty($params['detail_item'])) {
            $files = FoquzPollAnswerItemFile::findAll([
                'foquz_poll_answer_id' => $this->id,
                'foquz_question_id'    => $currentQuestion->id
            ]);
            if (!empty($files)) {
                foreach ($files as $file) {
                    if ($file->file_full_path && file_exists($file->file_full_path)) {
                        unlink($file->file_full_path);
                    }
                }
                FoquzPollAnswerItemFile::deleteAll(['id' => ArrayHelper::getColumn($files, 'id')]);
            }
        }

        if (!$answerForm->skipped && count($params) !== 0) {
            if ($currentQuestion->main_question_type === FoquzQuestion::TYPE_ASSESSMENT) {
                if ($currentQuestion->rating_type === FoquzQuestion::RATING_DISHES) {
                    $s = 0;
                    $answerForm->answer = $params['answer'] ?? null;
                    $countRates = 0;
                    foreach ($params['rating'] as $dishScore) {
                        $dishScoreModel = FoquzPollDishScore::find()
                            ->where(['answer_id' => $this->id])
                            ->andWhere(['dish_id' => $dishScore['dish_id']])
                            ->one();
                        if ($dishScore['score'] >= 1 && $dishScore['score'] <= 5) {
                            $dishScoreModel->score = $dishScore['score'];
                            $dishScoreModel->save();
                            $s += $dishScore['score'];
                            $countRates++;
                        }
                    }
                    $answerForm->rating = round($s / $countRates);
                } else {
                    $answerForm->self_variant = null;
                    $answerForm->is_self_variant = false;
                    $answerForm->answer = null;
                    if (isset($params['rating']) && $params['rating'] >= 1 && $params['rating'] <= 5) {
                        $answerForm->rating = $params['rating'];
                    }
                    if (isset($params['detail_item'])) {
                        if (in_array('is_self_answer', $params['detail_item'])) {
                            $key = array_search('is_self_answer', $params['detail_item']);
                            unset($params['detail_item'][$key]);
                            $answerForm->is_self_variant = true;
                            $answerForm->self_variant = $params['self_variant'] ?? $params[''];
                        }
                        $answerForm->detail_item = json_encode($params['detail_item']);
                    }
                    if (isset($params['answer'])) {
                        $answerForm->answer = $params['answer'];
                    }
                }
            } elseif ($currentQuestion->main_question_type === FoquzQuestion::TYPE_VARIANTS) {
                if (isset($params['detail_item'])) {
                    if (in_array('is_self_answer', $params['detail_item'])) {
                        if (!isset($params['self_variant']) ||
                            $params['self_variant'] === '') {
                            throw new BadRequestHttpException('Не заполнено поле Свой вариант');
                        }
                        $key = array_search('is_self_answer', $params['detail_item']);
                        unset($params['detail_item'][$key]);
                        $answerForm->is_self_variant = true;
                        $answerForm->self_variant = $params['self_variant'];
                    } else {
                        $answerForm->is_self_variant = false;
                        $answerForm->self_variant = null;
                    }
                    $answerForm->detail_item = json_encode(array_values($params['detail_item']));
                }
                if (isset($params['answer'])) {
                    $answerForm->answer = $params['answer'];
                }
            } elseif ($currentQuestion->main_question_type === FoquzQuestion::TYPE_FILIAL) {
                if (isset($params['detail_item'])) {
                    $filial = Filial::findOne($params['detail_item'][0]);
                    if ($filial) {
                        $this->answer_filial_id = $filial->id;
                    }
                    $answerForm->detail_item = $params['detail_item'];
                }
                if (isset($params['comment'])) {
                    $answerForm->self_variant = $params['comment'];
                    $answerForm->is_self_variant = true;
                }
            } elseif ($currentQuestion->main_question_type === FoquzQuestion::TYPE_DICTIONARY) {
                if (isset($params['detail_item'])) {
                    $element = DictionaryElement::findOne($params['detail_item'][0]);
                    if ($element) {
                        $this->answer_dictionary_element_id = $element->id;
                    }
                    $answerForm->detail_item = $params['detail_item'];
                }
                if (isset($params['comment'])) {
                    $answerForm->self_variant = $params['comment'];
                    $answerForm->is_self_variant = true;
                }
            } elseif (
                $currentQuestion->main_question_type === FoquzQuestion::TYPE_TEXT_ANSWER ||
                $currentQuestion->main_question_type === FoquzQuestion::TYPE_ADDRESS
            ) {
                $answerForm->rating = null;
                if ($currentQuestion->mask === 5) {
                    $answerForm->answer = json_encode($params['answer'] ?? [], JSON_UNESCAPED_UNICODE);
                } else {
                    $answerForm->answer = $params['answer'] ?? '';
                }
            } elseif ($currentQuestion->main_question_type === FoquzQuestion::TYPE_DATE) {
                $answerForm->rating = null;
                $dateInfo = [
                    'date' => $params['date'] ?? '',
                    'time' => isset($params['time']) ? str_replace(' ', '', $params['time']) : '',
                ];
                if ($currentQuestion->link_with_client_field && $answerForm->foquzPollAnswer->contact_id) {
                    $answerForm->foquzPollAnswer->contact->fillClientField($currentQuestion->linked_client_field,
                        date('Y-m-d H:i:s', strtotime($dateInfo['date'] . ' ' . $dateInfo['time'])),
                        $currentQuestion->rewrite_linked_field);
                }
                $answerForm->answer = json_encode($dateInfo);
            } elseif ($currentQuestion->main_question_type === FoquzQuestion::TYPE_FORM) {
                $answerForm->rating = null;
                foreach ($params as $id => $answer) {
                    $formField = FoquzQuestionFormField::findOne($id);
                    if ($formField) {
                        if (is_array($answer)) {
                            if ($formField->link_with_client_field && $answerForm->foquzPollAnswer->contact_id && count($answer) > 0) {
                                $answerForm->foquzPollAnswer->contact->fillClientField($formField->linked_client_field,
                                    $answer, $formField->rewrite_linked_field);
                            }
                        } else {
                            if ($formField->link_with_client_field && $answerForm->foquzPollAnswer->contact_id && strlen($answer) > 0) {
                                $answerForm->foquzPollAnswer->contact->fillClientField($formField->linked_client_field,
                                    $answer, $formField->rewrite_linked_field);
                            }
                        }
                    }
                }
                $answerForm->answer = json_encode($params, JSON_UNESCAPED_UNICODE);
            } elseif ($currentQuestion->main_question_type === FoquzQuestion::TYPE_PRIORITY) {
                // Костыли, но иначе при смене выбранного порядка не уходит запрос в БД
                if (!empty($answerForm->oldAttributes['detail_item'])) {
                    $oldAttributes = $answerForm->oldAttributes;
                    $oldAttributes['detail_item'] = null;
                    $answerForm->setOldAttributes($oldAttributes);
                }
                $answerForm->detail_item = $params['answer'] ?? null;
                $answerForm->self_variant = $params['comment'] ?? null;
            } elseif ($currentQuestion->main_question_type === FoquzQuestion::TYPE_CHOOSE_MEDIA) {
                $answerForm->answer = json_encode(($params['answer'] ?? []));
                $answerForm->self_variant = $params['comment'] ?? null;
            } elseif ($currentQuestion->main_question_type === FoquzQuestion::TYPE_GALLERY_RATING) {
                if (isset($params['answer']) && $params['answer']) {
                    $rates = [];
                    foreach ($params['answer'] as $media => $rating) {
                        if ($rating >= 1 && $rating <= 5) {
                            $rates[$media] = $rating;
                        }
                    }
                    $answerForm->answer = json_encode($rates);
                }
                $answerForm->self_variant = $params['comment'] ?? null;
            } elseif ($currentQuestion->main_question_type === FoquzQuestion::TYPE_SMILE_RATING) {
                if (isset($params['answer'])) {
                    $smilesIds = ArrayHelper::getColumn($currentQuestion->questionSmiles, 'id');
                    if (in_array($params['answer'], $smilesIds)) {
                        $answerForm->answer = $params['answer'];
                    }
                }
                if (isset($params['rating'])) {
                    $answerForm->rating = $currentQuestion->smiles_count ? min($params['rating'],
                        $currentQuestion->smiles_count) : $params['rating'];
                }
                $answerForm->self_variant = $params['comment'] ?? null;
                if (isset($params['detail_item']) && is_array($params['detail_item'])) {
                    $answerForm->detail_item = $params['detail_item'];
                }
            } elseif ($currentQuestion->main_question_type === FoquzQuestion::TYPE_SCALE ||
            $currentQuestion->main_question_type === FoquzQuestion::TYPE_DISTRIBUTION_SCALE) {
                if (!is_array($params['rating'])) {
                    $answerForm->rating = $params['rating'];
                } else {
                    $answerForm->answer = json_encode($params['rating']);
                }
                $answerForm->self_variant = $params['comment'] ?? null;
            } elseif ($currentQuestion->main_question_type === FoquzQuestion::TYPE_NPS_RATING) {
                if (!is_array($params['rating'])) {
                    $answerForm->rating = $params['rating'];
                } else {
                    $answerForm->answer = json_encode($params['rating']);
                }
                if (isset($params['detail_item']) && is_array($params['detail_item'])) {
                    $answerForm->detail_item = $params['detail_item'];
                }
                if (isset($params['self_variant']) && is_string($params['self_variant'])) {
                    $answerForm->self_variant = $params['self_variant'];
                } elseif (isset($params['answer']) && is_string($params['answer'])) {
                    $answerForm->self_variant = $params['answer'];
                } elseif (isset($params['comment']) && is_string($params['comment'])) {
                    $answerForm->self_variant = $params['comment'];
                }
                if ($answerForm->self_variant !== null && $answerForm->self_variant !== '') {
                    $answerForm->is_self_variant = true;
                }
            } elseif ($currentQuestion->main_question_type === FoquzQuestion::TYPE_SIMPLE_MATRIX) {
                if ($currentQuestion->random_variants_order) {
                    $matrixSettings = json_decode($currentQuestion->matrix_settings);
                    $answer = [];
                    if (is_array($params['answer'])) {
                        if (!$currentQuestion->donor) {
                            foreach ($matrixSettings->rows as $v) {
                                $answer[$v] = isset($params['answer'][$v]) ? $params['answer'][$v] : null;
                            }
                        } else {
                            foreach ($matrixSettings->donorRows as $v) {
                                $answer[$v] = isset($params['answer'][$v]) ? $params['answer'][$v] : null;
                            }
                        }
                    }
                } else {
                    $answer = isset($params['answer']) ? $params["answer"] : null;
                }
                $answerForm->answer = json_encode($answer, JSON_UNESCAPED_UNICODE) ?? '""';
                $answerForm->detail_item = $params['detail_item'] ?? null;
                $answerForm->self_variant = $params['comment'] ?? null;
            } elseif ($currentQuestion->main_question_type === FoquzQuestion::TYPE_SEM_DIFFERENTIAL) {
                $answerForm->answer = isset($params['answer']) ? json_encode($params['answer']) : null;
                $answerForm->self_variant = $params['comment'] ?? null;
            } elseif ($currentQuestion->main_question_type === FoquzQuestion::TYPE_VARIANT_STAR) {
                $answerForm->answer = isset($params['detail_item']) ? json_encode($params['detail_item'],
                    JSON_UNESCAPED_UNICODE) : null;
                $answerForm->self_variant = $params['comment'] ?? null;
            } elseif ($currentQuestion->main_question_type === FoquzQuestion::TYPE_FILE_UPLOAD) {
                $answerForm->rating = null;
                $answerForm->answer = $params['answer'] ?? null;
                if (!empty($params['detail_item']) && is_array($params['detail_item'])) {
                    $answerForm->save();
                    $fileIDs = $params['detail_item'];
                    if ($currentQuestion->files_length && $staffEditType !== self::STAFF_EDIT_FROM_SIDE_SHEET) {
                        $fileIDs = array_slice($params['detail_item'], 0, $currentQuestion->files_length);
                    }
                    FoquzPollAnswerItemFile::updateAll(
                        ['foquz_poll_answer_item_id' => $answerForm->id],
                        ['foquz_poll_answer_id' => $this->id, 'id' => $fileIDs]
                    );
                    /** @var FoquzPollAnswerItemFile[] $files */
                    $files = FoquzPollAnswerItemFile::find()
                        ->where([
                            'AND',
                            ['NOT IN', 'id', $fileIDs],
                            ['foquz_poll_answer_id' => $this->id, 'foquz_question_id' => $currentQuestion->id]
                        ])
                        ->all();
                    if (!empty($files)) {
                        foreach ($files as $file) {
                            if ($file->file_full_path && file_exists($file->file_full_path)) {
                                unlink($file->file_full_path);
                            }
                        }
                        FoquzPollAnswerItemFile::deleteAll(['id' => ArrayHelper::getColumn($files, 'id')]);
                    }
                }
            } elseif (in_array($currentQuestion->main_question_type,
                [FoquzQuestion::TYPE_STAR_RATING, FoquzQuestion::TYPE_RATING])) {
                if (isset($params["rating"]) && $params['rating'] >= 1 && $params['rating'] <= $currentQuestion->starRatingOptions->count) {
                    $answerForm->rating = $params['rating'];
                }
                if (isset($params['detail_item'])) {
                    if (in_array('is_self_answer', $params['detail_item'])) {
                        $key = array_search('is_self_answer', $params['detail_item']);
                        unset($params['detail_item'][$key]);
                        $answerForm->is_self_variant = true;
                        $answerForm->self_variant = $params['self_variant'] ?? $params[''];
                    }
                    $answerForm->detail_item = json_encode(array_values($params['detail_item']));
                }
                if (isset($params['answer'])) {
                    $answerForm->answer = $params['answer'];
                }
            } elseif ($currentQuestion->main_question_type === FoquzQuestion::TYPE_3D_MATRIX ||
                $currentQuestion->main_question_type === FoquzQuestion::TYPE_SIMPLE_MATRIX) {
                $answer = $params['answer'] ?? [];
                $answerForm->answer = json_encode($answer, JSON_UNESCAPED_UNICODE) ?? '""';
                $answerForm->self_variant = $params['comment'] ?? null;
            } elseif ($currentQuestion->main_question_type === FoquzQuestion::TYPE_CARD_SORTING_CLOSED) {
                $answerForm->self_variant = $params['comment'] ?? null;
                $answerForm->answer = json_encode(($params['answer'] ?? []));
            } elseif ($currentQuestion->main_question_type === FoquzQuestion::TYPE_FIRST_CLICK) {
                $di = $answerForm->detail_item;
                if (isset($params['time_expired'])) {
                    $di['time_expired'] = 1;
                }
                $di['answer'] = $params['point'] ?? [];
                $answerForm->detail_item = $di;
                $answerForm->answer = $params['answer'] ?? null;

            }
        }
        if (is_array($answerForm->answer)) {
            $answerForm->answer = json_encode($answerForm->answer, JSON_UNESCAPED_UNICODE) ?: null;
        }
        $answerForm->save();
        /** @var \yii\queue\amqp_interop\Queue $queue */
        $queue = \Yii::$app->rabbit_queue;

        if ($this->foquzPoll->point_system && $staffEditType !== self::STAFF_EDIT_FROM_SIDE_SHEET) {
            $answerForm->points_job_id = $queue
                ->push(new AnswerItemPointsJob([
                    'id' => $answerForm->id,
                ]));

            $this->points_job_id = $queue
                ->delay(1)
                ->push(new AnswerPointsJob([
                    'id' => $this->id,
                ]));
        }

        if (in_array($currentQuestion->main_question_type,
            [FoquzQuestion::TYPE_TEXT_ANSWER, FoquzQuestion::TYPE_ADDRESS])) {
            if (
                !$answerForm->skipped &&
                $answerForm->answer &&
                $answerForm->answer !== '""' &&
                $currentQuestion->link_with_client_field &&
                $answerForm->foquzPollAnswer->contact_id
            ) {
                $answerForm->foquzPollAnswer->contact->fillClientField($currentQuestion->linked_client_field,
                    $answerForm->answer, $currentQuestion->rewrite_linked_field);
            }
        }

        if ($this->status === self::STATUS_OPEN) {
            $this->status = self::STATUS_IN_PROGRESS;
            $this->user_id = Yii::$app->user->getId();

            if (isset(\Yii::$app->rabbit_queue)) {
                \Yii::$app->rabbit_queue->push(new PollNotificationJob([
                    'item_id' => $answerForm->id,
                ]));
            }
            $this->foquzPoll->company->updateTarriffsMoreLimitAnswers();
        }

        if (!$staffEditType) {
            $this->answer_time = time() - strtotime($this->first_question_showed_at ?? $this->created_at);
        }

        if ($this->save()) {
            $this->addProcessingIfNeeded();
        } else {
            throw new Exception('Не удалось сохранить анкету', $this->errors);
        }

        Yii::$app->cache->delete(['question_stat', 'id' => $currentQuestion->id]);

        $pointPercent = $this->max_points > 0 ? round(($this->points / $this->max_points) * 100) : 0;
        if ($pointPercent < 0) {
            $pointPercent = 0;
        }

        $ret = [
            $answerForm,
            [
                'lastQuestion' => (int)$lastQuestionId === (int)$currentQuestion->id,
                'points'       => $this->foquzPoll->point_system ? [
                    'points'  => $this->points,
                    'max'     => $this->max_points,
                    'percent' => $pointPercent,
                ] : null
            ]
        ];
        if ($currentQuestion->main_question_type === FoquzQuestion::TYPE_FIRST_CLICK) {
            $di = $answerForm->detail_item;
            $ret[1]['time_start'] = $di['time_start'] ?? null;
        }
        return $ret;
    }

    public function addProcessingIfNeeded()
    {
        if (FoquzPollAnswerProcessing::find()->where(['poll_answer_id' => $this->id])->exists()) {
            return null;
        }
        $settings = CompanyRequestProcessingSettings::findOne(['company_id' => $this->foquzPoll->company_id]);

        if (!$settings) {
            $settings = new CompanyRequestProcessingSettings;
            $settings->company_id = $this->foquzPoll->company_id;
            $settings->save();
        }
        if (!$settings->request_processing_enabled) {
            return;
        }

        $needed = false;

        if ($settings->answer_with_complain and $this->complaint
            and (!$settings->pollsForComplaints or in_array($this->foquz_poll_id,
                    ArrayHelper::getColumn($settings->pollsForComplaints, 'id')))
        ) {
            $needed = true;
        }

        if ($settings->rate_answer
            and (!$settings->pollsForRates or in_array($this->foquz_poll_id,
                    ArrayHelper::getColumn($settings->pollsForRates, 'id')))
        ) {
            foreach (FoquzPollAnswerItem::findAll(['foquz_poll_answer_id' => $this->id]) as $item) {
                if ($item->foquzQuestion->isRating()) {
                    if ($settings->percents) {
                        $rating = (new Query())->select([
                            'score_to_percents(
                                CASE 
                                    WHEN foquz_question.main_question_type IN (14,10) THEN avg_score(foquz_poll_answer_item.answer->"$.*") 
                                    WHEN foquz_question.main_question_type = 0 AND (SELECT COUNT(*) FROM foquz_poll_dish_score ds WHERE ds.answer_id=foquz_poll_answer_item.id) > 0 THEN (SELECT AVG(score) FROM foquz_poll_dish_score ds WHERE ds.answer_id=foquz_poll_answer_item.id GROUP BY(foquz_poll_answer_item.id))
                                    ELSE foquz_poll_answer_item.rating
                                END,
                                CASE WHEN foquz_question.main_question_type=12 AND foquz_question.from_one=0  THEN 0  ELSE 1 END,
                                CASE 
                                WHEN foquz_question.main_question_type=12  THEN 10 
                                WHEN foquz_question.main_question_type=11  THEN foquz_question.smiles_count 
                                WHEN foquz_question.main_question_type IN (15,18) THEN sr.count 
                                ELSE 5 END) as percentRating'
                        ])
                            ->from('foquz_poll_answer_item')
                            ->andWhere([
                                'in',
                                'main_question_type',
                                [
                                    FoquzQuestion::TYPE_ASSESSMENT,
                                    FoquzQuestion::TYPE_STAR_RATING,
                                    FoquzQuestion::TYPE_RATING,
                                    FoquzQuestion::TYPE_SEM_DIFFERENTIAL,
                                    FoquzQuestion::TYPE_SMILE_RATING,
                                    FoquzQuestion::TYPE_GALLERY_RATING,
                                    FoquzQuestion::TYPE_NPS_RATING,
                                ]
                            ])
                            ->andWhere(['foquz_poll_answer_item.id' => $item->id])
                            ->innerJoin('foquz_question',
                                'foquz_question.id = foquz_poll_answer_item.foquz_question_id')
                            ->leftJoin('foquz_question_star_rating_options sr',
                                'sr.foquz_question_id=foquz_question.id')
                            ->scalar();
                        if ($rating >= 0 && $rating <= $settings->percents) {
                            $needed = true;
                            break;
                        }
                    } else {
                        $needed = true;
                        break;
                    }
                }
            }

        }
        if ($settings->rate_answer_with_comment
            and (!$settings->pollsForRatesWithComment or in_array($this->foquz_poll_id,
                    ArrayHelper::getColumn($settings->pollsForRatesWithComment, 'id')))
        ) {
            foreach (FoquzPollAnswerItem::findAll(['foquz_poll_answer_id' => $this->id]) as $item) {
                if ($item->foquzQuestion->isRating() and $item->getCommentColumn()) {
                    $needed = true;
                    break;
                }
            }
        }
        if (!$settings->answer_with_complain && !$settings->rate_answer && !$settings->rate_answer_with_comment) {
            $needed = true;
        }

        if ($needed and is_null($this->processing)) {
            $processing = new FoquzPollAnswerProcessing([
                'poll_answer_id' => $this->id,
                'status'         => FoquzPollAnswerProcessing::STATUS_NEW,
            ]);
            $processing->save();
            $this->refresh("processing");
        }

        return $processing ?? null;
    }

    /**
     * Возвращает минимальную оценку по анкете в процентах
     * @return int|null
     */
    public function getMinPercentScore(): ?int
    {
        $minPercent = null;
        foreach ($this->foquzAnswer as $answer) {
            $percent = $answer->itemPercentScore;
            if ($minPercent === null || ($percent !== null && $percent < $minPercent)) {
                $minPercent = $percent;
            }
        }
        return $minPercent;
    }


    /**
     * @param string $key
     * @return string
     */
    public function getAnswerValue(string $key): string
    {
        if ($key === 'answerTime') {
            return $this->answerTime();
        }
        if ($key === 'id') {
            return  $this->hash_id ? $this->hash_id : "";
            /*$d = $this->id % 100;
            if ($d == 0) {
                $d = 1;
            }
            return (strtotime($this->created_at) - 1557158285) * $d;*/
        }
        if ($key === 'pollId') {
            return $this->foquzPoll->id;
        }
        if ($key === 'type') {
            return $this->foquzPoll->is_auto ? 'Автоматический' : 'Ручной';
        }
        if ($key === 'pollName') {
            return $this->foquzPoll->name ?: '';
        }
        if ($key === 'passedAt') {
            return date('d.m.Y H:i', strtotime($this->updated_at));
        }
        if ($key === 'processingStatus') {
            $status = '';
            if ($this->processing) {
                $status = FoquzPollAnswerProcessing::getStatuses()[$this->processing->status];
            }
            return $status;
        }
        if ($key === 'status') {
            return self::getStatuses()[$this->status] ?? '';
        }
        if ($key === 'processingTime' && $this->processing && $this->processing->process_up) {
            $time = '';
            if ($this->processing && $this->processing->process_up) {
                $time = date('d.m.Y', strtotime($this->processing->process_up));
            }
            return $time;
        }
        if ($key === 'channel') {
            $channel = '';
            if ($this->answerChannel && $this->answerChannel->channel_name) {
                $channel = $this->answerChannel->channel_name;
                if (
                    $this->answerChannel->channel_name !== 'Тест'
                    && $this->answerChannel->channel_name !== 'Пройден за клиента'
                    && $this->answerChannel->channel_name !== 'Пройден за респондента'
                ) {

                    $channel .= ', повтор: ' . $this->getSendRepeatValue() ?? '1';
                }
            }
            return $channel;
        }
        if ($key === 'device') {
            return $this->device ? $this->deviceText : '';
        }
        if ($key === 'clientPersonalData') {
            return $this->user_agreement ? 'Да' : '';
        }
        if ($key === 'lang') {
            return $this->pollLang ? $this->pollLang->name : '';
        }
        if ($key === 'mailing') {
            return $this->mailingListSend[0]->mailingListContact->mailingList->name ?? '';
        }
        if ($key === 'comments') {
            $comments = [];
            foreach ($this->foquzAnswer as $foquzPollAnswerItem) {
                if (!in_array($foquzPollAnswerItem->foquzQuestion->main_question_type, [
                    FoquzQuestion::TYPE_ASSESSMENT,
                    FoquzQuestion::TYPE_VARIANTS,
                    FoquzQuestion::TYPE_FILE_UPLOAD,
                    FoquzQuestion::TYPE_RATING,
                    FoquzQuestion::TYPE_STAR_RATING,
                    FoquzQuestion::TYPE_INTERMEDIATE_BLOCK,
                ])) {
                    if ($foquzPollAnswerItem->foquzQuestion->main_question_type === FoquzQuestion::TYPE_NPS_RATING) {
                        $comments[] = ExtraQuestionService::getCommentColumnNps($foquzPollAnswerItem);
                    } elseif ($foquzPollAnswerItem->foquzQuestion->main_question_type === FoquzQuestion::TYPE_VARIANT_STAR) {
                        $comments[] = ExtraQuestionService::getCommentColumnVariantStar($foquzPollAnswerItem);
                    } elseif ($foquzPollAnswerItem->foquzQuestion->main_question_type === FoquzQuestion::TYPE_SMILE_RATING) {
                        $comment = ExtraQuestionService::getCommentColumnSmileRating($foquzPollAnswerItem, true);
                        if ($comment) {
                            $comments[] = $comment;
                        }
                    } else {
                        $comments[] = $foquzPollAnswerItem->self_variant ?? null;
                    }
                } elseif ((int)$foquzPollAnswerItem->foquzQuestion->main_question_type === FoquzQuestion::TYPE_VARIANTS) {
                    if ($foquzPollAnswerItem->answer) {
                        $comments[] = $foquzPollAnswerItem->answer;
                    }
                    if ($foquzPollAnswerItem->self_variant) {
                        $comments[] = $foquzPollAnswerItem->self_variant;
                    }
                    if (!$foquzPollAnswerItem->answer && !$foquzPollAnswerItem->self_variant) {
                        $comments[] = '';
                    }
                } else {
                    $comments[] = $foquzPollAnswerItem->self_variant ?: $foquzPollAnswerItem->answer;
                }
            }
            $comments = $comments ? implode("\n", $comments) : '';
            return trim($comments, " \n");
        }
        if ($key === 'filial') {
            $filialName = '';
            if ($this->foquzPoll->is_auto) {
                if ($this->order && $this->order->filial) {
                    if ($categoryId = $this->order->filial->category_id) {
                        $filialName = FilialCategory::findOne($categoryId)->name . ' / ' . $this->order->filial->name;
                    } else {
                        $filialName = $this->order->filial->name;
                    }
                }
            } else {
                if ($this->answerFilial) {
                    if ($categoryId = $this->answerFilial->category_id) {
                        $filialName = FilialCategory::findOne($categoryId)->name . ' / ' . $this->answerFilial->name;
                    } else {
                        $filialName = $this->answerFilial->name;
                    }
                }
            }
            return $filialName;
        }
        if ($key === 'complaint') {
            return $this->complaint ? $this->complaint->text : '';
        }
        if ($key === 'pointsCollect') {
            $pointPercent = $this->max_points > 0 ? round(($this->points / $this->max_points) * 100) : 0;
            if ($pointPercent < 0) {
                $pointPercent = 0;
            }

            $points = $this->foquzPoll->point_system ? [
                'answer_points' => $this->points ?? 0,
                'points_max'    => $this->max_points,
                'percent'       => $pointPercent,
            ] : '';
            if ($points) {
                $points = $points['answer_points'] . ' из ' . $points['points_max'] . ', ' . $points['percent'] . '%';
            }
            return $points ?: '';
        }
        if ($key === 'orderNumber') {
            return $this->order->number ?? $this->order->id ?? '';
        }
        if ($key === 'orderTime') {
            return isset($this->order->created_time) ? date('d.m.Y H:i', strtotime($this->order->created_time)) : '';
        }
        if ($key === 'orderSum') {
            return $this->order->sum ?? '';
        }
        if ($key === 'orderType') {
            return $this->order->delivery->name ?? '';
        }
        if ($key === 'sourceType') {
            return $this->order->source->name ?? '';
        }
        if ($key === 'orderAddress') {
            return $this->order->address ?? '';
        }
        if ($key === 'filialClient') {
            $filialsContact = '';
            if ($this->contact && count($this->contact->contactFilials) > 0) {
                $filialsContact = implode(",", ArrayHelper::getColumn($this->contact->contactFilials, "filial.name"));
            }
            return $filialsContact;
        }
        if ($key === 'moderator') {
            $moderator = '';
            if ($this->processing && $this->processing->moderator) {
                if ($name = $this->processing->moderator->name) {
                    $moderator = $name;
                }
                if (!$moderator && $username = $this->processing->moderator->username) {
                    $moderator = $username;
                }
            }
            return $moderator;
        }
        if ($key === 'executor') {
            $executor = '';
            if ($this->processing && $this->processing->executor) {
                if ($name = $this->processing->executor->name) {
                    $executor = $name;
                }
                if (!$executor && $username = $this->processing->executor->username) {
                    $executor = $username;
                }
            }
            return $executor;
        }
        if ($key === 'ip') {
            return $this->ip_address ?? '';
        }
        if ($key === 'os') {
            return $this->os ?? '';
        }
        if ($key === 'browser') {
            return $this->useragent ?? '';
        }

        if ($key === 'clientName') {
            return $this->contact ? implode(' ',
                [$this->contact->last_name, $this->contact->first_name, $this->contact->patronymic]) : '';
        }
        if ($key === 'clientPhone') {
            return isset($this->contact->formattedPhone) ? trim($this->contact->formattedPhone, '+') : '';
        }
        if ($key === 'clientEmail') {
            return $this->contact->email ?? '';
        }
        if ($key === 'clientCustomerId') {
            return $this->contact->company_client_id ?? '';
        }
        if ($key === 'clientFilials') {
            $filials = [];
            if ($this->contact && $this->contact->contactFilials) {
                $contactFilials = array_map(
                    function ($item) {
                        return $item->filial_id;
                    },
                    $this->contact->contactFilials
                );
                foreach ($contactFilials as $contactFilial) {
                    $filial = Filial::findOne($contactFilial);
                    if ($filial) {
                        if ($categoryId = $filial->category_id) {
                            $filials[] = FilialCategory::findOne($categoryId)->name . ' / ' . $filial->name;
                        } else {
                            $filials[] = $filial->name;
                        }
                    }
                }
            }
            return implode(', ', $filials);
        }
        if ($key === 'clientGender') {
            return isset($this->contact->gender) ? (FoquzContact::GENDERS[$this->contact->gender] ?? '') : '';
        }
        if ($key === 'clientBirthday') {
            return isset($this->contact->birthday) && $this->contact->birthday ? date('d.m.Y',
                strtotime($this->contact->birthday)) : '';
        }
        if ($key === 'clientTags') {
            if ($this->contact && $this->contact->tags) {
                $contactTags = array_map(function ($item) {
                    return $item->tag;
                }, $this->contact->tags);
                $contactTags = implode(', ', $contactTags);
            }
            return $contactTags ?? '';
        }
        if ($key === 'clientAddedAt') {
            return $this->contact ? date("d.m.Y", $this->contact->created_at) : '';
        }
        if ($key === 'clientUpdatedAt') {
            return $this->contact ? date("d.m.Y", $this->contact->updated_at) : '';
        }
        $computedFields = ($this->contact && $this->contact->computedFields) ? $this->contact->computedFields->toObject() : '';
        if ($key === 'clientLtv') {
            return $computedFields && $computedFields['ltv_amount'] ? $computedFields['ltv_amount'] : '';
        }
        if ($key === 'clientLastOrderDate') {
            return $computedFields && $computedFields['last_order_date'] ?
                date('d.m.Y', strtotime($computedFields['last_order_date'])) : '';
        }
        if ($key === 'answerTags') {
            return implode(';', ArrayHelper::getColumn($this->tags,'tag'));
        }
        $additionalFields = [];
        if ($this->contact && count($this->contact->additionalFieldValues)) {
            foreach ($this->contact->additionalFieldValues as $afv) {
                $additionalFields['client' . $afv->additional_field_id] = $afv->value;
            }
            if (isset($additionalFields[$key])) {
                return $additionalFields[$key];
            }
        }

        return '';
    }

    public function answerTime($withHours = false): string
    {
        if ($this->answer_time) {
            $time = $this->answer_time;
        } else {
            $startAt = $this->first_question_showed_at ? strtotime($this->first_question_showed_at) : strtotime($this->created_at);
            $time = strtotime($this->updated_at) - $startAt;
        }
        $minutes = floor($time / 60);
        $seconds = $time - ($minutes * 60);
        $hours = floor($minutes / 60);
        $minutes -= ($hours * 60);
        $seconds = strlen($seconds) === 1 ? 0 . $seconds : $seconds;
        $minutes = strlen($minutes) === 1 ? 0 . $minutes : $minutes;
        $hours = strlen($hours) === 1 ? 0 . $hours : $hours;
        if ($withHours && $hours === '00') {
            return $minutes . ':' . $seconds;
        }

        return $hours . ':' . $minutes . ':' . $seconds;
    }

    public function getSendRepeatValue()
    {
        $i = 1;
        foreach ($this->sends as $send) {
            if ($send->status == FoquzPollMailingListSend::STATUS_OPEN) {
                return $i;
            }
            $i++;
        }
        return $i;
    }

    public function getAnswerItems(): array
    {
        $answers = [];
        $items = ArrayHelper::index($this->foquzAnswer, 'foquz_question_id');
        foreach ($this->foquzPoll->foquzQuestions as $question) {
            $rating = 0;
            if ($question->main_question_type === FoquzQuestion::TYPE_INTERMEDIATE_BLOCK) {
                continue;
            }
            /** @var FoquzPollAnswerItem $item */
            $item = $items[$question->id] ?? null;
            if (!$item || $item->isEmpty) {
                $answers[] = [
                    'question'    => (object)['name' => $question->service_name],
                    'type'        => $question->main_question_type,
                    'ratingType'  => $question->rating_type,
                    'smileType'   => $question->smile_type,
                    'smilesCount' => $question->smiles_count,
                    'starCount'   => $question->starRatingOptions->count ?? null,
                    'answered'    => false,
                ];
                continue;
            }
            if ($question->rating_type === FoquzQuestion::RATING_DISHES) {
                $n1 = 0;
                $s = 0;
                foreach ($this->foquzPollDishes as $dish) {
                    $n1++;
                    $s += (int)$dish->score;
                }
            } elseif (in_array($question->main_question_type, [
                FoquzQuestion::TYPE_GALLERY_RATING,
                FoquzQuestion::TYPE_SEM_DIFFERENTIAL,
            ], true)) {
                $decoded = json_decode($item->answer, true) ?? [];
                $decoded = array_values($decoded);
                $item['rating'] = count($decoded) ? array_sum($decoded) / count($decoded) : null;
            }
            $variant_skipped = 0;
            $answer = [];
            if (
                $question->main_question_type === FoquzQuestion::TYPE_VARIANT_STAR ||
                $question->main_question_type === FoquzQuestion::TYPE_SIMPLE_MATRIX ||
                ($question->main_question_type === FoquzQuestion::TYPE_NPS_RATING && $question->set_variants) ||
                ($question->main_question_type === FoquzQuestion::TYPE_SCALE && $question->set_variants) ||
                ($question->main_question_type === FoquzQuestion::TYPE_DISTRIBUTION_SCALE)
            ) {
                if ($answerItems = $answer = json_decode($item->answer, true)) {
                    if (isset($answerItems['extra'])) {
                        unset($answerItems['extra']);
                    } elseif (($question->main_question_type === FoquzQuestion::TYPE_SIMPLE_MATRIX || $question->main_question_type === FoquzQuestion::TYPE_NPS_RATING) &&
                        $item->detail_item
                    ) {
                        $answer['extra'] = $item->detail_item;
                    }
                    if (in_array('null', $answerItems) || in_array('', $answerItems)) {
                        $variant_skipped = 1;
                    }
                    $rating = array_sum($answerItems) / count($answerItems);
                }
            } else {
                $rating = $item->rating;
            }

            if ($question->main_question_type === FoquzQuestion::TYPE_SMILE_RATING) {
                if ($item->detail_item) {
                    $answer['extra'] = $item->detail_item;
                }
            }
            $selfAnswer = $item->self_variant;
            if (in_array($question->main_question_type, FoquzQuestion::TYPES_COMMENTED, true)) {
                $comment = $item->self_variant ?? null;

                if ($question->main_question_type === FoquzQuestion::TYPE_TEXT_ANSWER) {
                    if ($question->mask === FoquzQuestion::MASK_NAME) {
                        $comment = $item->answer != "" ? json_decode($item->answer) : null;
                    } else {
                        $comment = $item->answer != "" ? $item->answer : null;
                    }
                } elseif ($question->main_question_type === FoquzQuestion::TYPE_VARIANTS) {
                    $comment = $item->answer ?: null;
                } elseif ($question->main_question_type === FoquzQuestion::TYPE_SMILE_RATING) {
                    $detail = $item->detail_item ?? [];
                    if (isset($detail['self_variant'])) {
                        $comment = $detail['self_variant'];
                    } elseif (isset($detail['text_answer'])) {
                        $comment = $detail['text_answer'];
                    } else {
                        foreach ($detail as $detailItem) {
                            if (isset($detailItem['self_variant'])) {
                                $comment = $detailItem['self_variant'];
                            }
                        }
                    }
                }
            } else {
                $comment = $item->self_variant ?: $item->answer;
            }
            if ($comment) {
                $hasTextAnswer = 1;
            }
            if (!empty($question->service_name)) {
                $name = $question->service_name;
            } elseif (!empty($question->name)) {
                $name = $question->name;
            } else {
                $name = $question->description;
            }

            if ($question->main_question_type == FoquzQuestion::TYPE_FILIAL) {
                $selectedIDs = $item->detail_item ?? [];
                foreach ($selectedIDs as $selectedID) {
                    $filial = Filial::findOne($selectedID);
                    $answer[] = $filial;
                }
            }

            if ($question->donor) {
                $variants = [];
                /** @var RecipientQuestionDetail[] $recipientVariants */
                $recipientVariants = $question->getRecipientQuestionDetails()->joinWith('questionDetail')->all();
                foreach ($recipientVariants as $variant) {
                    if ($variant->questionDetail) {
                        $variants[$variant->questionDetail->id] = [
                            'id'             => $variant->questionDetail->id,
                            'question'       => $variant->questionDetail->question,
                            'is_deleted'     => $variant->questionDetail->is_deleted,
                            'extra_question' => $variant->questionDetail->extra_question,
                            'need_extra'     => $variant->need_extra,
                        ];
                    } else {
                        $variants[-1] = [
                            'id'             => -1,
                            'question'       => $question->getMainDonor()->self_variant_text ?: 'Свой вариант',
                            'extra_question' => 0,
                            'need_extra'     => $variant->need_extra,
                        ];
                    }
                }
                foreach ($question->questionDetails as $variant) {
                    if (!$variant->extra_question) {
                        continue;
                    }
                    if (!isset($variants[$variant->id])) {
                        $variants[$variant->id] = [
                            'id'             => $variant->id,
                            'question'       => $variant->question,
                            'is_deleted'     => $variant->is_deleted,
                            'extra_question' => $variant->extra_question,
                            'need_extra'     => $variant->need_extra,
                        ];
                    }
                }
                $variants = array_values($variants);
            } else {
                $variants = ArrayHelper::toArray($question->questionDetails);
            }

            if ($question->main_question_type === FoquzQuestion::TYPE_SEM_DIFFERENTIAL) {
                $rating = null;
            }
            if ($question->main_question_type === FoquzQuestion::TYPE_FIRST_CLICK) {
                $answer['points'] = FoquzQuestionFirstClick::parseAnswer($item->detail_item);
            }
            $answers[] = [
                'question'           => (object)['name' => $name],
                'rating'             => $question->rating_type === FoquzQuestion::RATING_DISHES ? ($n1 ? round($s / $n1) : 0) : $rating,
                'type'               => $question->main_question_type,
                'ratingType'         => $question->rating_type,
                'smileType'          => $question->smile_type,
                'smilesCount'        => $question->smiles_count,
                'starCount'          => $question->starRatingOptions->count ?? null,
                'answered'           => true,
                'comment'            => $comment,
                'self_answer'        => $selfAnswer,
                'created_at'         => $item->created_at,
                'skipped'            => $item->skipped,
                'variant_skipped'    => $variant_skipped,
                'smiles'             => $question->main_question_type === FoquzQuestion::TYPE_SMILE_RATING ? $item->foquzQuestionSmiles : null,
                'scaleRatingSetting' => $question->scaleRatingSetting ?? null,
                'setVariants'        => $question->set_variants == 1,
                'variants'           => $variants,
                'answer'             => $answer,
            ];
        }

        return $answers;
    }

    public function getAnswerDetails(): array
    {
        $answers = [];
        foreach ($this->foquzAnswer as $item) {

            if ($item->foquzQuestion->rating_type === FoquzQuestion::RATING_DISHES) {

            }

            $answers[] = [
                'question'    => (object)['name' => $item->foquzQuestion->service_name],
                'rating'      => $item->foquzQuestion->rating_type === FoquzQuestion::RATING_DISHES ? ($n1 ? round($s / $n1) : 0) : $rating,
                'type'        => $item->foquzQuestion->main_question_type,
                'ratingType'  => $item->foquzQuestion->rating_type,
                'smileType'   => $item->foquzQuestion->smile_type,
                'smilesCount' => $item->foquzQuestion->smiles_count,
                'starCount'   => $item->foquzQuestion->starRatingOptions->count ?? null,
                'answered'    => true,
                'comment'     => in_array($item->foquzQuestion->main_question_type, [
                    FoquzQuestion::TYPE_TEXT_ANSWER,
                    FoquzQuestion::TYPE_DATE,
                    FoquzQuestion::TYPE_ADDRESS,
                    FoquzQuestion::TYPE_FORM,
                    FoquzQuestion::TYPE_PRIORITY,
                    FoquzQuestion::TYPE_GALLERY_RATING,
                    FoquzQuestion::TYPE_CHOOSE_MEDIA,
                    FoquzQuestion::TYPE_SMILE_RATING,
                    FoquzQuestion::TYPE_NPS_RATING,
                    FoquzQuestion::TYPE_SIMPLE_MATRIX,
                    FoquzQuestion::TYPE_SEM_DIFFERENTIAL,
                    FoquzQuestion::TYPE_VARIANT_STAR
                ], true) ? ($item->self_variant ?? null) : ($item->self_variant ?: $item->answer),
                'created_at'  => $item->created_at,
                'skipped'     => $item->skipped,
                'smiles'      => $item->foquzQuestionSmiles,
            ];
        }

        return $answers;
    }

    public function buildHash()
    {
        return $this->hash_id;
        /*
         * $d = $this->id % 100;
        if ($d == 0) {
            $d = 1;
        }

        return (strtotime($this->created_at) - 1557158285) * ($d);
        */
    }

    /** Возвращает имя филиала с которого прошли опрос
     *
     * @return string
     */
    public function filialName()
    {
        $filialName = '';
        if ($this->foquzPoll->is_auto) {
            if ($this->order && $this->order->filial) {
                if ($categoryId = $this->order->filial->category_id) {
                    $filialName = FilialCategory::findOne($categoryId)->name . '/' . $this->order->filial->name;
                } else {
                    $filialName = $this->order->filial->name;
                }
            }
        } else {
            if ($this->answerFilial) {
                if ($categoryId = $this->answerFilial->category_id) {
                    $filialName = FilialCategory::findOne($categoryId)->name . '/' . $this->answerFilial->name;
                } else {
                    $filialName = $this->answerFilial->name;
                }
            }
        }

        if (empty($filialName)) {
            $filialName = 'anonymous';
        }

        return $filialName;
    }

    public function getAnswerContentForEmail(): array
    {
        $content = [];
        $i = 1;
        $answerItems = ArrayHelper::index($this->foquzAnswer, 'foquz_question_id');
        $hiddenQuestions = FoquzPollAnswerHiddenQuestion::find()
            ->select('question_id')
            ->where(['answer_item_id' => ArrayHelper::getColumn($this->foquzAnswer, 'id')])
            ->column();
        $companyDomain = Yii::$app->params['protocol'] . '://' . $this->foquzPoll->company->alias;
        foreach ($this->foquzPoll->foquzQuestions as $question) {
            if ($question->main_question_type === FoquzQuestion::TYPE_INTERMEDIATE_BLOCK || $question->is_deleted) {
                continue;
            }
            /** @var FoquzPollAnswerItem|array $answerItem */
            $answerItem = $answerItems[$question->id] ?? [];
            $itemContent = [];
            $questionName = $question->description;
            $hiddenQuestion = in_array($question->id, $hiddenQuestions);
            if (empty($answerItem) || $answerItem->isEmpty) {
                $itemContent[] = [
                    'question'           => $questionName,
                    'type_name'          => $question->typeName,
                    'is_required'        => false,
                    'is_variant'         => false,
                    'is_question_hidden' => $hiddenQuestion,
                    'answers'            => [
                        [
                            'type' => 'question-skipped',
                        ],
                    ],
                ];
            } elseif ($answerItem->skipped) {
                switch ($question->main_question_type) {
                    case FoquzQuestion::TYPE_RATING:
                    case FoquzQuestion::TYPE_STAR_RATING:
                    case FoquzQuestion::TYPE_SCALE:
                    case FoquzQuestion::TYPE_GALLERY_RATING:
                    case FoquzQuestion::TYPE_SMILE_RATING:
                    case FoquzQuestion::TYPE_NPS_RATING:
                    case FoquzQuestion::TYPE_VARIANT_STAR:
                    case FoquzQuestion::TYPE_SEM_DIFFERENTIAL:
                        $itemContent[] = [
                            'question'           => $questionName,
                            'type_name'          => $question->typeName,
                            'is_variant'         => false,
                            'is_required'        => $question->is_required,
                            'is_question_hidden' => $hiddenQuestion,
                            'answers'            => [
                                [
                                    'type' => 'rating-skipped',
                                ],
                            ],
                        ];
                        break;
                    case FoquzQuestion::TYPE_VARIANTS:
                    case FoquzQuestion::TYPE_CHOOSE_MEDIA:
                    case FoquzQuestion::TYPE_SIMPLE_MATRIX:
                    case FoquzQuestion::TYPE_3D_MATRIX:
                    case FoquzQuestion::TYPE_DICTIONARY:
                    case FoquzQuestion::TYPE_TEXT_ANSWER:
                        $itemContent[] = [
                            'question'           => $questionName,
                            'type_name'          => $question->typeName,
                            'is_variant'         => false,
                            'is_required'        => $question->is_required,
                            'is_question_hidden' => $hiddenQuestion,
                            'answers'            => [
                                [
                                    'type' => 'answer-skipped',
                                ],
                            ],
                        ];
                        break;
                }
            } else {
                if ($question->main_question_type === FoquzQuestion::TYPE_SCALE && $question->set_variants) {
                    $question->main_question_type = FoquzQuestion::TYPE_SCALE . '1';
                }
                switch ($question->main_question_type) {
                    case FoquzQuestion::TYPE_STAR_RATING: // Звездный рейтинг
                    case FoquzQuestion::TYPE_RATING: // Рейтинг
                    case FoquzQuestion::TYPE_SCALE: // Шкала
                        $labels = json_decode($question->starRatingOptions->labels ?? '', false) ?? [];
                        $mark = $labels[$answerItem->rating - 1] ?? '';
                        if (!empty($mark)) {
                            $mark = " (" . $mark . ")";
                        }

                        if (in_array($question->main_question_type,
                            [FoquzQuestion::TYPE_STAR_RATING, FoquzQuestion::TYPE_RATING], true)) {
                            $max = $question->starRatingOptions->count;
                        } elseif ($question->main_question_type === FoquzQuestion::TYPE_SCALE) {
                            $max = $question->scaleRatingSetting->end ?? 100;
                        } else {
                            $max = 5;
                        }
                        $itemContent[] = [
                            'question'           => $questionName,
                            'type_name'          => $question->typeName,
                            'is_required'        => $question->is_required,
                            'is_variant'         => false,
                            'is_question_hidden' => $hiddenQuestion,
                            'answers'            => [
                                [
                                    'type'  => 'rating',
                                    'value' => $answerItem->rating,
                                    'max'   => $max,
                                    'mark'  => $mark,
                                ],
                            ],
                        ];
                        if (!empty($question->detail_question)) {
                            $answers = [];
                            if (!empty($answerItem->answer) || !empty($answerItem->detail_item)) {
                                if ($question->variants_element_type === 2) {
                                    $answers[] = ['type' => 'answer', 'value' => $answerItem->answer];
                                } else {
                                    if (is_array(json_decode($answerItem->answer, true))) {
                                        $answerArray = json_decode($answerItem->answer, true);
                                    } elseif (json_decode($answerItem->detail_item, true)) {
                                        $answerArray = json_decode($answerItem->detail_item);
                                    } else {
                                        $answerArray = [];
                                    }
                                    $variants = \yii\helpers\ArrayHelper::map($question->questionDetails, 'id',
                                        'question');
                                    foreach ($answerArray as $item) {
                                        $variantName = $variants[$item] ?? '';
                                        $answers[] = ['type' => 'answer', 'value' => $variantName];
                                    }
                                }
                                $itemContent[] = [
                                    'question'    => $question->detail_question,
                                    'is_required' => false,
                                    'is_variant'  => true,
                                    'answers'     => $answers,
                                ];
                                if (!empty($answerItem->self_variant)) {
                                    $itemContent[] = [
                                        'question'    => $question->self_variant_text ?: 'Свой вариант',
                                        'is_required' => false,
                                        'is_variant'  => true,
                                        'answers'     => [
                                            [
                                                'type'  => 'answer',
                                                'value' => $answerItem->self_variant,
                                            ],
                                        ],
                                    ];
                                }
                            }
                        }
                        break;
                    case FoquzQuestion::TYPE_VARIANT_STAR: // Звездный рейтинг для вариантов
                    case FoquzQuestion::TYPE_SCALE . '1': // Шкала для вариантов
                        if ($question->main_question_type === FoquzQuestion::TYPE_SCALE . '1') {
                            $question->main_question_type = FoquzQuestion::TYPE_SCALE;
                        }
                        $answer = json_decode($answerItem->answer, true) ?? [];
                        $labels = json_decode($question->starRatingOptions->labels ?? '', false) ?? [];
                        $itemContent[] = [
                            'question'           => $question->name,
                            'type_name'          => $question->typeName,
                            'is_required'        => $question->is_required,
                            'is_variant'         => false,
                            'is_question_hidden' => $hiddenQuestion,
                            'answers'            => [],
                        ];
                        if ($question->main_question_type === FoquzQuestion::TYPE_VARIANT_STAR) {
                            $max = $question->starRatingOptions->count;
                        } elseif ($question->main_question_type === FoquzQuestion::TYPE_SCALE) {
                            $max = $question->scaleRatingSetting->end;
                        } else {
                            $max = 5;
                        }
                        $details = [];
                        if (!$question->donor) {
                            $details = $question->questionDetails;
                        } elseif ($question->getMainDonor()->main_question_type !== FoquzQuestion::TYPE_DICTIONARY) {
                            $details = $question->recipientsQuestionDetails;
                            ArrayHelper::multisort($details, 'position');
                            $foquzAnswers = ArrayHelper::index($this->foquzAnswer, 'foquz_question_id') ?? [];
                            $donorAnswerItem = $foquzAnswers[$question->donor] ?? null;
                            $donorVariants = array_values(json_decode($donorAnswerItem->detail_item, true) ?? []);
                            if (isset($answer['-1'])) {
                                $detail = new FoquzQuestionDetail();
                                $detail->id = -1;
                                $detail->question = $donorAnswerItem->self_variant;
                                $detail->need_extra = RecipientQuestionDetail::find()->where([
                                    'recipient_id'       => $question->id,
                                    'question_detail_id' => null,
                                    'need_extra'         => 1
                                ])->exists();
                                $details[] = $detail;
                            }
                        } else {
                            $dictionaryElements = DictionaryElement::findAll(array_keys($answer));
                            foreach ($dictionaryElements as $element) {
                                $detail = new FoquzQuestionDetail();
                                $detail->id = $element->id;
                                $detail->question = $element->fullPath;
                                $detail->need_extra = RecipientQuestionDetail::find()->where([
                                    'recipient_id'          => $question->id,
                                    'dictionary_element_id' => $element->id,
                                    'need_extra'            => 1
                                ])->exists();
                                $details[] = $detail;
                            }
                        }
                        foreach ($details as $detail) {
                            if ($detail->extra_question) {
                                continue;
                            }
                            $answers = [];
                            if (isset($answer[$detail->id]) && $answer[$detail->id] !== 'null' && $answer[$detail->id] !== '0') {
                                $allFieldsEmpty = false;
                                $mark = $labels[$answer[$detail->id] - 1] ?? '';
                                if (!empty($mark)) {
                                    $mark = " (" . $mark . ")";
                                }
                                $itemContent[] = [
                                    'question'    => $detail->question,
                                    'is_required' => false,
                                    'is_variant'  => true,
                                    'answers'     => [
                                        [
                                            'type'  => 'rating',
                                            'value' => $answer[$detail->id],
                                            'max'   => $max,
                                            'mark'  => $mark,
                                        ],
                                    ],
                                ];
                                if (!empty($question->detail_question) && $detail->need_extra && ($question->for_all_rates ||
                                        ($answer[$detail->id] >= $question->starRatingOptions->extra_question_rate_from &&
                                            $answer[$detail->id] <= $question->starRatingOptions->extra_question_rate_to
                                        ))) {
                                    if ($question->variants_element_type === 2) {
                                        $answers[] = [
                                            'type'  => 'answer',
                                            'value' => $answer['extra'][$detail->id]['answer'] ?? ''
                                        ];
                                    } else {
                                        $variants = \yii\helpers\ArrayHelper::map($question->questionDetails, 'id',
                                            'question');
                                        foreach ($answer['extra'][$detail->id] ?? [] as $key => $item) {
                                            $variantName = $variants[$item] ?? '';
                                            if (!empty($variantName)) {
                                                $answers[] = ['type' => 'answer', 'value' => $variantName];
                                            }
                                        }
                                    }
                                    if (!empty($answers) || !empty($answer['extra'][$detail->id]['self_variant'])) {
                                        $itemContent[] = [
                                            'question'    => $question->detail_question,
                                            'is_required' => false,
                                            'is_variant'  => true,
                                            'answers'     => $answers,
                                        ];
                                    }
                                    if (!empty($answer['extra'][$detail->id]['self_variant'])) {
                                        $itemContent[] = [
                                            'question'    => $question->self_variant_text ?: 'Свой вариант',
                                            'is_required' => false,
                                            'is_variant'  => true,
                                            'answers'     => [
                                                [
                                                    'type'  => 'answer',
                                                    'value' => $answer['extra'][$detail->id]['self_variant'],
                                                ],
                                            ],
                                        ];
                                    }
                                    if (empty($answers) && empty($answer['extra'][$detail->id]['self_variant'])) {
                                        $itemContent[] = [
                                            'question'    => $detail->question,
                                            'is_required' => false,
                                            'is_variant'  => true,
                                            'answers'     => [
                                                [
                                                    'type' => 'answer-skipped',
                                                ],
                                            ],
                                        ];
                                    }
                                }
                            } elseif (!$question->donor || in_array($detail->id, $donorVariants)) {
                                if (isset($answer[$detail->id]) && $answer[$detail->id] === 'null') {
                                    $type = 'rating-skipped';
                                } else {
                                    $type = 'answer-empty';
                                }
                                $itemContent[] = [
                                    'question'    => $detail->question,
                                    'is_required' => false,
                                    'is_variant'  => true,
                                    'answers'     => [
                                        [
                                            'type' => $type,
                                        ],
                                    ],
                                ];
                            }
                        }
                        $itemContent = ArrayHelper::merge([
                            [
                                'question'           => $questionName,
                                'type_name'          => $question->typeName,
                                'is_required'        => $question->is_required,
                                'is_variant'         => false,
                                'is_question_hidden' => $hiddenQuestion,
                                'answers'            => [],
                            ],
                        ], $itemContent);
                        break;
                    case FoquzQuestion::TYPE_NPS_RATING:
                        $answers = [];
                        if ($answerItem->rating === -1) {
                            $answers[] = [
                                'type' => 'question-skipped',
                            ];
                        } elseif ($answerItem->skipped) {
                            $answers[] = [
                                'type' => 'rating-skipped',
                            ];
                        } else {
                            $marks = [
                                'Критик'    => [0, 6],
                                'Нейтрал'   => [7, 8],
                                'Промоутер' => [9, 10],
                            ];
                            $mark = '';
                            if (!$question->set_variants) {
                                $allFieldsEmpty = false;
                                foreach ($marks as $key => $range) {
                                    if ($answerItem->rating >= $range[0] && $answerItem->rating <= $range[1]) {
                                        $mark = $key;
                                        break;
                                    }
                                }
                                $answers[] = [
                                    'type'  => 'nps',
                                    'value' => $answerItem->rating,
                                    'mark'  => $mark,
                                ];
                            } else {
                                $answer = json_decode($answerItem->answer, true) ?? [];
                                $allFieldsEmpty = true;
                                $details = [];
                                if (!$question->donor) {
                                    $details = $question->questionDetails;
                                } elseif ($question->getMainDonor()->main_question_type !== FoquzQuestion::TYPE_DICTIONARY) {
                                    $details = $question->recipientsQuestionDetails;
                                    ArrayHelper::multisort($details, 'position');
                                    $foquzAnswers = ArrayHelper::index($this->foquzAnswer, 'foquz_question_id') ?? [];
                                    $donorAnswerItem = $foquzAnswers[$question->donor] ?? null;
                                    $donorVariants = $donorAnswerItem->detail_item;
                                    if (is_string($donorVariants)) {
                                        $donorVariants = json_decode($donorAnswerItem->detail_item, true) ?? [];
                                    }
                                    $donorVariants = array_values($donorVariants);
                                    if (isset($answer['-1'])) {
                                        $detail = new FoquzQuestionDetail();
                                        $detail->id = -1;
                                        $detail->question = $donorAnswerItem->self_variant;
                                        $details[] = $detail;
                                    }
                                } else {
                                    $dictionaryElements = DictionaryElement::findAll(array_keys($answer));
                                    foreach ($dictionaryElements as $element) {
                                        $detail = new FoquzQuestionDetail();
                                        $detail->id = $element->id;
                                        $detail->question = $element->fullPath;
                                        $detail->need_extra = RecipientQuestionDetail::find()->where([
                                            'recipient_id'          => $question->id,
                                            'dictionary_element_id' => $element->id,
                                            'need_extra'            => 1
                                        ])->exists();
                                        $details[] = $detail;
                                    }
                                }
                                $extraVariants = ArrayHelper::index($question->questionDetails, null,
                                    'extra_question')[1] ?? [];
                                $extraVariants = ArrayHelper::map($extraVariants, 'id', 'question');
                                foreach ($details as $detail) {
                                    if ($detail->extra_question) {
                                        continue;
                                    }
                                    if (isset($answer[$detail->id]) && $answer[$detail->id] !== '-1') {
                                        $allFieldsEmpty = false;
                                        $mark = '';
                                        foreach ($marks as $key => $range) {
                                            if ($answer[$detail->id] >= $range[0] && $answer[$detail->id] <= $range[1]) {
                                                $mark = $key;
                                                break;
                                            }
                                        }
                                        $itemContent[] = [
                                            'question'    => $detail->question,
                                            'is_required' => false,
                                            'is_variant'  => true,
                                            'answers'     => [
                                                [
                                                    'type'  => 'nps',
                                                    'value' => $answer[$detail->id],
                                                    'mark'  => $mark,
                                                ],
                                            ],
                                        ];
                                        $extraAnswers = [];
                                        if ($question->detail_question !== null && $detail->need_extra && $question->extra_question_type !== FoquzQuestion::EXTRA_QUESTION_COMMON) {
                                            if (is_array($answerItem->detail_item)) {
                                                $answerArray = $answerItem->detail_item;
                                            } else {
                                                $answerArray = [];
                                            }
                                            if (
                                                $question->variants_element_type === FoquzQuestion::VARIANT_ELEMENT_TYPE_TEXT &&
                                                isset($answerArray[$detail->id]['answer'])
                                            ) {
                                                $extraAnswers[] = [
                                                    'type'  => 'answer',
                                                    'value' => $answerArray[$detail->id]['answer']
                                                ];
                                            } else {
                                                foreach ($answerArray[$detail->id] ?? [] as $key => $item) {
                                                    if (in_array($key, ['answer', 'self_variant'])) {
                                                        continue;
                                                    }
                                                    $variantName = $extraVariants[$item] ?? '';
                                                    $extraAnswers[] = ['type' => 'answer', 'value' => $variantName];
                                                }
                                            }
                                            $itemContent[] = [
                                                'question'    => $question->detail_question,
                                                'is_required' => false,
                                                'is_variant'  => true,
                                                'answers'     => $extraAnswers,
                                            ];
                                            if (
                                                isset($answerArray[$detail->id]['self_variant']) &&
                                                $answerArray[$detail->id]['self_variant'] !== '' &&
                                                $question->variants_element_type !== FoquzQuestion::VARIANT_ELEMENT_TYPE_TEXT
                                            ) {
                                                $itemContent[] = [
                                                    'question'    => $question->self_variant_text ?: 'Свой вариант',
                                                    'is_required' => false,
                                                    'is_variant'  => true,
                                                    'answers'     => [
                                                        [
                                                            'type'  => 'answer',
                                                            'value' => $answerArray[$detail->id]['self_variant'],
                                                        ],
                                                    ],
                                                ];
                                            }
                                        }
                                    } elseif (!$question->donor || in_array($detail->id, $donorVariants)) {
                                        $itemContent[] = [
                                            'question'    => $detail->question,
                                            'is_required' => false,
                                            'is_variant'  => true,
                                            'answers'     => [
                                                [
                                                    'type' => 'rating-skipped',
                                                ],
                                            ],
                                        ];
                                    }
                                }
                                if ($question->extra_question_type === FoquzQuestion::EXTRA_QUESTION_COMMON) {
                                    $extraAnswers = [];
                                    if (!empty($answerItem->is_self_variant) || !empty($answerItem->detail_item)) {
                                        if ($question->variants_element_type === FoquzQuestion::VARIANT_ELEMENT_TYPE_TEXT) {
                                            $extraAnswers[] = [
                                                'type'  => 'answer',
                                                'value' => $answerItem->self_variant
                                            ];
                                        } else {
                                            if (is_array($answerItem->detail_item)) {
                                                $answerArray = $answerItem->detail_item;
                                            } else {
                                                $answerArray = [];
                                            }
                                            $variants = ArrayHelper::index($question->questionDetails, null,
                                                'extra_question')[1] ?? [];
                                            $variants = ArrayHelper::map($variants, 'id', 'question');
                                            foreach ($answerArray as $item) {
                                                $variantName = $variants[$item] ?? '';
                                                $extraAnswers[] = ['type' => 'answer', 'value' => $variantName];
                                            }
                                        }
                                        $itemContent[] = [
                                            'question'    => $question->detail_question,
                                            'is_required' => false,
                                            'is_variant'  => true,
                                            'answers'     => $extraAnswers,
                                        ];
                                        if (
                                            $answerItem->self_variant !== null &&
                                            $answerItem->self_variant !== '' &&
                                            $question->variants_element_type !== FoquzQuestion::VARIANT_ELEMENT_TYPE_TEXT
                                        ) {
                                            $itemContent[] = [
                                                'question'    => $question->self_variant_text ?: 'Свой вариант',
                                                'is_required' => false,
                                                'is_variant'  => true,
                                                'answers'     => [
                                                    [
                                                        'type'  => 'answer',
                                                        'value' => $answerItem->self_variant,
                                                    ],
                                                ],
                                            ];
                                        }
                                    }
                                }
                            }
                        }
                        if (!empty($allFieldsEmpty)) {
                            $itemContent = [];
                            $answers = [
                                [
                                    'type' => 'question-skipped',
                                ],
                            ];
                        }
                        $itemContent = ArrayHelper::merge([
                            [
                                'question'           => $questionName,
                                'type_name'          => $question->typeName,
                                'is_required'        => $question->is_required,
                                'is_variant'         => false,
                                'is_question_hidden' => $hiddenQuestion,
                                'answers'            => $answers,
                            ],
                        ], $itemContent);
                        break;
                    case FoquzQuestion::TYPE_TEXT_ANSWER:
                        $mask = $question->mask ? $question->mask : 0;
                        $answers = $this->getTextFieldContentForEmail($answerItem->answer,
                            $question->variants_element_type, $mask);
                        $allFieldsEmpty = true;
                        foreach ($answers as $answer) {
                            if (!empty(str_replace(' ', '', $answer['value']))) {
                                $allFieldsEmpty = false;
                            }
                        }
                        if ($allFieldsEmpty) {
                            $answers = [
                                [
                                    'type' => 'question-skipped',
                                ],
                            ];
                        }
                        $itemContent[] = [
                            'question'           => $questionName,
                            'type_name'          => $question->typeName,
                            'is_required'        => $question->is_required,
                            'is_variant'         => false,
                            'is_question_hidden' => $hiddenQuestion,
                            'answers'            => $answers,
                        ];
                        break;
                    case FoquzQuestion::TYPE_SMILE_RATING:
                        if ($answerItem->rating === 0) {
                            $itemContent[] = [
                                'question'           => $questionName,
                                'type_name'          => $question->typeName,
                                'is_required'        => $question->is_required,
                                'is_variant'         => false,
                                'is_question_hidden' => $hiddenQuestion,
                                'answers'            => [
                                    [
                                        'type' => 'question-skipped',
                                    ],
                                ],
                            ];
                        } else {
                            $smiles = ArrayHelper::index($question->questionSmiles, 'id');
                            $mark = $smiles[$answerItem->answer]->label ?? '';
                            if (!empty($mark)) {
                                $mark = " (" . $mark . ")";
                            }
                            $itemContent[] = [
                                'question'           => $questionName,
                                'type_name'          => $question->typeName,
                                'is_required'        => $question->is_required,
                                'is_variant'         => false,
                                'is_question_hidden' => $hiddenQuestion,
                                'answers'            => [
                                    [
                                        'type'  => 'rating',
                                        'value' => $answerItem->rating,
                                        'max'   => count($smiles),
                                        'mark'  => $mark,
                                    ],
                                ],
                            ];
                        }
                        break;
                    case FoquzQuestion::TYPE_VARIANTS:
                        $answers = [];
                        $emptyVariants = [];
                        $answerArray = json_decode($answerItem->detail_item, true) ?? [];
                        if (!$question->donor) {
                            $questionDetails = $question->questionDetails;
                            ArrayHelper::multisort($questionDetails, 'position');
                            $variants = \yii\helpers\ArrayHelper::map($questionDetails, 'id', 'question');
                            foreach ($questionDetails as $detail) {
                                if ($detail->is_empty) {
                                    $emptyVariants[] = $detail->id;
                                }
                            }
                        } elseif ($question->getMainDonor()->main_question_type !== FoquzQuestion::TYPE_DICTIONARY) {
                            $recipientsQuestionDetails = $question->recipientsQuestionDetails;
                            ArrayHelper::multisort($recipientsQuestionDetails, 'position');
                            $variants = \yii\helpers\ArrayHelper::map($recipientsQuestionDetails, 'id', 'question');
                        } else {
                            $variants = \yii\helpers\ArrayHelper::map(DictionaryElement::findAll($answerArray), 'id',
                                'fullPath');
                        }
                        foreach ($answerArray as $item) {
                            $variantName = $variants[$item] ?? '';
                            if ($item === '-1') {
                                $foquzAnswers = ArrayHelper::index($this->foquzAnswer, 'foquz_question_id') ?? [];
                                /** @var FoquzPollAnswerItem $donorAnswerItem */
                                $donorAnswerItem = $foquzAnswers[$question->donor] ?? null;
                                $variantName = $donorAnswerItem->self_variant ?? '';
                            }
                            if (!empty($variantName)) {
                                $answers[] = ['type' => 'answer', 'value' => $variantName];
                            }
                            if (in_array($item, $emptyVariants)) {
                                /** @var FoquzFile|null $file */
                                $file = FoquzFile::find()->where([
                                    'entity_type' => FoquzFile::TYPE_DETAIL,
                                    'entity_id'   => $item
                                ])->one();
                                if ($file) {
                                    $answers[] = [
                                        'type'  => 'link',
                                        'name'  => pathinfo($file->origin_name, PATHINFO_FILENAME),
                                        'value' => !empty($file->getFileUrl()) ? $companyDomain . $file->getFileUrl() : '',
                                    ];
                                }
                            }
                        }
                        $itemContent[] = [
                            'question'           => $questionName,
                            'type_name'          => $question->typeName,
                            'is_required'        => $question->is_required,
                            'is_variant'         => false,
                            'is_question_hidden' => $hiddenQuestion,
                            'answers'            => $answers,
                        ];
                        if (!empty($answerItem->self_variant)) {
                            $itemContent[] = [
                                'question'    => $question->self_variant_text ?: 'Свой вариант',
                                'is_required' => false,
                                'is_variant'  => true,
                                'answers'     => [
                                    [
                                        'type'  => 'answer',
                                        'value' => $answerItem->self_variant,
                                    ],
                                ],
                            ];
                        }
                        break;
                    case FoquzQuestion::TYPE_DATE:
                        $answer = json_decode($answerItem->answer) ?? [];
                        $itemContent[] = [
                            'question'           => $questionName,
                            'type_name'          => $question->typeName,
                            'is_required'        => $question->is_required,
                            'is_variant'         => false,
                            'is_question_hidden' => $hiddenQuestion,
                            'answers'            => [
                                [
                                    'type'  => 'answer',
                                    'value' => (!empty($answer->date) ? $answer->date . ' ' : '') . $answer->time ?: '',
                                ],
                            ],
                        ];
                        break;
                    case FoquzQuestion::TYPE_FILE_UPLOAD:
                        $files = $answerItem->answerItemFiles;
                        $answers = [];
                        foreach ($files as $file) {
                            $answers[] = [
                                'type' => 'file',
                                'name' => $file->origin_name,
                                'url'  => 'https://' . $question->poll->company->alias . '/' . $file->file_path,
                            ];
                        }
                        $itemContent[] = [
                            'question'           => $questionName,
                            'type_name'          => $question->typeName,
                            'is_required'        => $question->is_required,
                            'is_variant'         => false,
                            'is_question_hidden' => $hiddenQuestion,
                            'answers'            => $answers,
                        ];
                        break;
                    case FoquzQuestion::TYPE_ADDRESS:
                        if (!empty($answerItem->answer)) {
                            $itemContent[] = [
                                'question'           => $questionName,
                                'type_name'          => $question->typeName,
                                'is_required'        => $question->is_required,
                                'is_variant'         => false,
                                'is_question_hidden' => $hiddenQuestion,
                                'answers'            => [
                                    [
                                        'type'  => 'answer',
                                        'value' => $answerItem->answer,
                                    ],
                                ],
                            ];
                        } else {
                            $itemContent[] = [
                                'question'           => $questionName,
                                'type_name'          => $question->typeName,
                                'is_required'        => $question->is_required,
                                'is_variant'         => false,
                                'is_question_hidden' => $hiddenQuestion,
                                'answers'            => [
                                    [
                                        'type' => 'question-skipped',
                                    ],
                                ],
                            ];
                        }
                        break;
                    case FoquzQuestion::TYPE_FORM:
                        $answer = json_decode($answerItem->answer, false) ?? [];
                        $allFieldsEmpty = true;
                        foreach ($question->formFields as $formField) {
                            $id = (string)$formField->id;
                            if (!empty($answer->$id) && ($formField->mask_type !== FoquzQuestion::MASK_NAME ||
                                    !empty($answer->$id->name) || !empty($answer->$id->surname) || !empty($answer->$id->patronymic))) {
                                $answersContent = $this->getTextFieldContentForEmail(
                                    $answer->$id,
                                    $formField->variants_type,
                                    $formField->mask_type
                                );
                                foreach ($answersContent as $answersContentItem) {
                                    if (!empty(str_replace(' ', '', $answersContentItem['value']))) {
                                        $allFieldsEmpty = false;
                                    }
                                }
                            } else {
                                $answersContent = [
                                    [
                                        'type' => 'answer-empty',
                                    ],
                                ];
                            }
                            $itemContent[] = [
                                'question'    => $formField->name,
                                'is_required' => false,
                                'is_variant'  => true,
                                'answers'     => $answersContent,
                            ];
                        }
                        if ($allFieldsEmpty) {
                            $itemContent = [
                                [
                                    'question'           => $questionName,
                                    'type_name'          => $question->typeName,
                                    'is_required'        => $question->is_required,
                                    'is_variant'         => false,
                                    'is_question_hidden' => $hiddenQuestion,
                                    'answers'            => [
                                        [
                                            'type' => 'question-skipped',
                                        ],
                                    ],
                                ],
                            ];
                        } else {
                            $itemContent = ArrayHelper::merge([
                                [
                                    'question'           => $questionName,
                                    'type_name'          => $question->typeName,
                                    'is_required'        => $question->is_required,
                                    'is_variant'         => false,
                                    'is_question_hidden' => $hiddenQuestion,
                                    'answers'            => [],
                                ],
                            ], $itemContent);
                        }
                        break;
                    case FoquzQuestion::TYPE_PRIORITY:
                        $answers = [];
                        $answerArray = $answerItem->detail_item;
                        if (!$question->donor) {
                            $variants = \yii\helpers\ArrayHelper::map($question->questionDetails, 'id', 'question');
                        } elseif ($question->getMainDonor()->main_question_type !== FoquzQuestion::TYPE_DICTIONARY) {
                            $recipientsQuestionDetails = $question->recipientsQuestionDetails;
                            ArrayHelper::multisort($recipientsQuestionDetails, 'position');
                            $variants = \yii\helpers\ArrayHelper::map($recipientsQuestionDetails, 'id', 'question');
                        } else {
                            $variants = \yii\helpers\ArrayHelper::map(DictionaryElement::findAll($answerArray), 'id',
                                'fullPath');
                        }
                        foreach ($answerArray as $item) {
                            if ($item === '-1') {
                                $foquzAnswers = ArrayHelper::index($this->foquzAnswer, 'foquz_question_id') ?? [];
                                /** @var FoquzPollAnswerItem $donorAnswerItem */
                                $donorAnswerItem = $foquzAnswers[$question->donor] ?? null;
                                $answers[] = $donorAnswerItem->self_variant ?? '';
                            } else {
                                $answers[] = $variants[$item] ?? '';
                            }
                        }
                        $itemContent[] = [
                            'question'           => $questionName,
                            'type_name'          => $question->typeName,
                            'is_required'        => $question->is_required,
                            'is_variant'         => false,
                            'is_question_hidden' => $hiddenQuestion,
                            'answers'            => [
                                [
                                    'type'   => 'priority',
                                    'values' => $answers,
                                ],
                            ],
                        ];
                        break;
                    case FoquzQuestion::TYPE_FILIAL:
                        $filialName = '';
                        $answer = $answerItem->detail_item;
                        $filial_id = !empty($answer[0]) ? $answer[0] : null;
                        if ($filial = Filial::findOne($filial_id)) {
                            $filialName = $filial->name;
                        }
                        if (!empty($filialName)) {
                            $answers = [
                                [
                                    'type'  => 'answer',
                                    'value' => $filialName,
                                ],
                            ];
                        } else {
                            $answers = [
                                [
                                    'type' => 'question-skipped',
                                ],
                            ];
                        }
                        $itemContent[] = [
                            'question'           => $questionName,
                            'type_name'          => $question->typeName,
                            'is_required'        => $question->is_required,
                            'is_variant'         => false,
                            'is_question_hidden' => $hiddenQuestion,
                            'answers'            => $answers,
                        ];
                        break;
                    case FoquzQuestion::TYPE_GALLERY_RATING:
                        $answer = json_decode($answerItem->answer, true) ?? [];
                        $files = ArrayHelper::index($question->questionFiles, 'id');
                        $j = 0;
                        $itemContent[] = [
                            'question'           => $questionName,
                            'type_name'          => $question->typeName,
                            'is_required'        => $question->is_required,
                            'is_variant'         => false,
                            'is_question_hidden' => $hiddenQuestion,
                            'answers'            => [],
                        ];
                        foreach ($answer as $key => $value) {
                            /** @var FoquzQuestionFile $file */
                            $file = ArrayHelper::getValue($files, $key, []);
                            $itemContent[] = [
                                'is_variant' => true,
                                'answers'    => [
                                    [
                                        'type'  => 'link',
                                        'name'  => 'Элемент ' . ++$j,
                                        'value' => !empty($file->link) ? $file->link : '',
                                    ],
                                    [
                                        'type'  => 'rating',
                                        'value' => $value,
                                        'max'   => 5,
                                    ],
                                ],
                            ];
                        }
                        break;
                    case FoquzQuestion::TYPE_SEM_DIFFERENTIAL:
                        $answer = json_decode($answerItem->answer, true) ?? [];
                        $itemContent[] = [
                            'question'           => $questionName,
                            'type_name'          => $question->typeName,
                            'is_required'        => $question->is_required,
                            'is_variant'         => false,
                            'is_question_hidden' => $hiddenQuestion,
                            'answers'            => [],
                        ];
                        foreach ($question->differentialRows as $differentialRow) {
                            $itemContent[] = [
                                'question'    => $differentialRow->start_label . ' — ' . $differentialRow->end_label,
                                'is_required' => false,
                                'is_variant'  => true,
                                'answers'     => [
                                    [
                                        'type'  => 'rating',
                                        'value' => $answer[$differentialRow->id] ?? 0,
                                        'max'   => 5,
                                    ],
                                ],
                            ];
                        }
                        break;
                    case FoquzQuestion::TYPE_ASSESSMENT:
                        $detailAnswers = json_decode($answerItem->detail_item) ?? [];
                        $answers = [];
                        if (!empty($answerItem->rating) && $question->rating_type !== FoquzQuestion::RATING_DISHES) {
                            $answers = [
                                [
                                    'type'  => 'rating',
                                    'value' => $answerItem->rating,
                                    'max'   => 5,
                                ],
                            ];
                        } elseif (empty($answerItem->rating) && $question->rating_type !== FoquzQuestion::RATING_DISHES) {
                            $variants = \yii\helpers\ArrayHelper::map($question->questionDetails, 'id', 'question');
                            foreach ($detailAnswers as $item) {
                                $variantName = $variants[$item] ?? '';
                                $answers[] = ['type' => 'answer', 'value' => $variantName];
                            }
                        } elseif ($question->rating_type === FoquzQuestion::RATING_DISHES) {
                            $dishesIDs = ArrayHelper::getColumn(ArrayHelper::toArray($this->foquzPollDishes),
                                'dish_id');
                            /** @var Dish[] $dishes */
                            $dishes = ArrayHelper::index(Dish::find()->where(['id' => $dishesIDs])->all(), 'id');
                            /** @var FoquzPollDishScore $pollDish */
                            foreach ($this->foquzPollDishes as $pollDish) {
                                $itemContent[] = [
                                    'question'    => $dishes[$pollDish->dish_id]->name,
                                    'is_required' => false,
                                    'is_variant'  => true,
                                    'answers'     => [
                                        [
                                            'type'  => 'rating',
                                            'value' => $pollDish->score,
                                            'max'   => 5,
                                        ],
                                    ],
                                ];
                            }
                        }
                        $itemContent = ArrayHelper::merge([
                            [
                                'question'           => $questionName . ($this->foquzPoll->is_auto ? ' — ' . $question->service_name : ''),
                                'type_name'          => $question->typeName,
                                'is_required'        => $question->is_required,
                                'is_variant'         => false,
                                'is_question_hidden' => $hiddenQuestion,
                                'answers'            => $answers,
                            ],
                        ], $itemContent);
                        $answers = [];
                        if ($question->rating_type !== FoquzQuestion::RATING_DISHES && !empty($question->detail_question) &&
                            ($question->for_all_rates || $answerItem->rating < 5)) {
                            $variants = \yii\helpers\ArrayHelper::map($question->questionDetails, 'id', 'question');
                            foreach ($detailAnswers as $item) {
                                $variantName = $variants[$item] ?? '';
                                $answers[] = ['type' => 'answer', 'value' => $variantName];
                            }
                            $itemContent[] = [
                                'question'    => $question->detail_question,
                                'is_required' => false,
                                'is_variant'  => true,
                                'answers'     => $answers,
                            ];
                        }
                        if (!empty($answerItem->self_variant)) {
                            $itemContent[] = [
                                'question'    => $question->self_variant_text ?: 'Свой вариант',
                                'is_required' => false,
                                'is_variant'  => true,
                                'answers'     => [
                                    [
                                        'type'  => 'answer',
                                        'value' => $answerItem->self_variant,
                                    ],
                                ],
                            ];
                        }
                        break;
                    case FoquzQuestion::TYPE_CHOOSE_MEDIA:
                        $answer = json_decode($answerItem->answer, true) ?? [];
                        $files = ArrayHelper::index($question->questionFiles, 'id');
                        $links = [];
                        $firstItem = true;
                        foreach ($answer as $value) {
                            /** @var FoquzQuestionFile $file */
                            $file = ArrayHelper::getValue($files, $value, []);
                            $index = array_search($value, array_keys($files)) + 1;
                            $links[] = [
                                'first_item' => $firstItem,
                                'name'       => ($file->type === 'video' ? 'Видео ' : 'Изображение ') . $index,
                                'value'      => !empty($file->link) ? $file->link : '',
                            ];
                            $firstItem = false;
                        }
                        $itemContent[] = [
                            'question'           => $questionName,
                            'type_name'          => $question->typeName,
                            'is_required'        => $question->is_required,
                            'is_variant'         => false,
                            'is_question_hidden' => $hiddenQuestion,
                            'answers'            => [
                                [
                                    'type'   => 'links',
                                    'values' => $links,
                                ],
                            ],
                        ];
                        break;
                    case FoquzQuestion::TYPE_DICTIONARY:
                        $selectedItems = $answerItem->detail_item ?? [];
                        $answers = [];
                        foreach ($selectedItems as $selectedItem) {
                            $element = DictionaryElement::findOne($selectedItem);
                            if ($element) {
                                [$categories, $element] = DictionariesHelper::compactTree($element->treeElements);
                                $answers[] = [
                                    'type'       => 'dictionary',
                                    'categories' => implode(' / ', ArrayHelper::getColumn($categories, 'title')),
                                    'element'    => $element['title'],
                                ];
                            }
                        }
                        if (empty($answers)) {
                            $answers[] = [
                                'type' => 'question-skipped',
                            ];
                        }
                        $itemContent[] = [
                            'question'           => $questionName,
                            'type_name'          => $question->typeName,
                            'is_required'        => $question->is_required,
                            'is_variant'         => false,
                            'is_question_hidden' => $hiddenQuestion,
                            'answers'            => $answers,
                        ];
                        break;
                    case FoquzQuestion::TYPE_SIMPLE_MATRIX:
                        $answer = json_decode($answerItem->answer, true) ?? [];
                        $answers = [];
                        $detail = $answerItem->detail_item ?? [];
                        $settings = json_decode($question->matrix_settings, true) ?? [];
                        if (empty($settings['rows'])) {
                            break;
                        }
                        if (!isset($settings['extra_question']['rows'])) {
                            $settings['extra_question']['rows'] = [];
                        }
                        if (!isset($settings['extra_question']['cols'])) {
                            $settings['extra_question']['cols'] = [];
                        }
                        if ($question->donor) {
                            $foquzAnswers = ArrayHelper::index($this->foquzAnswer, 'foquz_question_id') ?? [];
                            $donorAnswerItem = $foquzAnswers[$question->donor] ?? null;
                            $donorVariants = $donorAnswerItem->detail_item;
                            if (is_string($donorVariants)) {
                                $donorVariants = json_decode($donorAnswerItem->detail_item, true) ?? [];
                            }
                            $donorVariants = array_values($donorVariants);
                            if (isset($answer['-1'])) {
                                $settings['rows'][] = $donorAnswerItem->self_variant;
                                $settings['donorRows'][] = -1;
                                if (in_array($question->getMainDonor()->self_variant_text ?: 'Свой вариант',
                                    $settings['extra_question']['rows'])) {
                                    $settings['extra_question']['rows'][] = $donorAnswerItem->self_variant;
                                }
                                if (isset($detail[-1])) {
                                    $detail[$donorAnswerItem->self_variant] = $detail[-1];
                                    unset($detail[-1]);
                                }
                            }
                        }
                        foreach ($settings['rows'] as $key => $row) {
                            $rowItemContent = [];
                            if ($question->donor) {
                                $donorRowId = $settings['donorRows'][$key] ?? null;
                                $answerValue = $answer[$donorRowId] ?? null;
                            } else {
                                $answerValue = $answer[$row] ?? null;
                            }
                            if ($answerValue && $answerValue !== 'null') {
                                $rowAnswers = [];
                                foreach ($answerValue as $value) {
                                    $rowAnswers[] = [
                                        'type'  => 'answer',
                                        'value' => $value,
                                    ];
                                }
                                $answers = $rowAnswers;
                                $detailAnswers = [];
                                if (!empty($question->detail_question) && ($question->for_all_rates ||
                                        empty($settings['extra_question']['rows']) || in_array($row,
                                            $settings['extra_question']['rows']))) {
                                    $haveExtra = false;
                                    if (!is_array($answerValue)) {
                                        $rowValues = [$answerValue];
                                    } else {
                                        $rowValues = $answerValue;
                                    }
                                    foreach ($rowValues as $rowValue) {
                                        if (in_array($rowValue, $settings['extra_question']['cols'])) {
                                            $haveExtra = true;
                                            break;
                                        }
                                    }
                                    if ($haveExtra) {
                                        if ($question->variants_element_type === 2) {
                                            $detailAnswers[] = [
                                                'type'  => 'answer',
                                                'value' => $detail[$row]['answer'] ?? ''
                                            ];
                                        } else {
                                            $variants = \yii\helpers\ArrayHelper::map($question->questionDetails, 'id',
                                                'question');
                                            foreach ($detail[$row] ?? [] as $item) {
                                                $variantName = $variants[$item] ?? '';
                                                if (!empty($variantName)) {
                                                    $detailAnswers[] = ['type' => 'answer', 'value' => $variantName];
                                                }
                                            }
                                        }
                                        if (!empty($detailAnswers)) {
                                            $rowItemContent[] = [
                                                'question'    => $question->detail_question,
                                                'is_required' => false,
                                                'is_variant'  => true,
                                                'answers'     => $detailAnswers,
                                            ];
                                        }
                                        if (!empty($detail[$row]['self_variant'])) {
                                            $rowItemContent[] = [
                                                'question'    => $question->self_variant_text ?: 'Свой вариант',
                                                'is_required' => false,
                                                'is_variant'  => true,
                                                'answers'     => [
                                                    [
                                                        'type'  => 'answer',
                                                        'value' => $detail[$row]['self_variant'],
                                                    ],
                                                ],
                                            ];
                                        }
                                    }
                                }
                            } elseif (!$question->donor || in_array($donorRowId, $donorVariants)) {
                                $answers = [
                                    [
                                        'type' => 'answer-skipped',
                                    ],
                                ];
                            } else {
                                continue;
                            }
                            $rowItemContent = ArrayHelper::merge([
                                [
                                    'question'    => $row,
                                    'is_required' => false,
                                    'is_variant'  => true,
                                    'answers'     => $answers,
                                ],
                            ], $rowItemContent);
                            $itemContent = ArrayHelper::merge($itemContent, $rowItemContent);
                        }
                        $itemContent = ArrayHelper::merge([
                            [
                                'question'           => $questionName,
                                'type_name'          => $question->typeName,
                                'is_required'        => $question->is_required,
                                'is_variant'         => false,
                                'is_question_hidden' => $hiddenQuestion,
                                'answers'            => [],
                            ],
                        ], $itemContent);
                        break;
                    case FoquzQuestion::TYPE_3D_MATRIX:
                        $answer = json_decode($answerItem->answer, true) ?? [];
                        $rowItemContent = [];
                        $matrixElements = ArrayHelper::index($question->matrixElements, null, 'type_id');
                        /** @var FoquzQuestionMatrixElement[] $columns */
                        $columns = !empty($matrixElements[FoquzQuestionMatrixElement::TYPE_COLUMN]) ?
                            ArrayHelper::index($matrixElements[FoquzQuestionMatrixElement::TYPE_COLUMN], 'id') : [];
                        /** @var FoquzQuestionMatrixElement[] $rows */
                        $rows = !empty($matrixElements[FoquzQuestionMatrixElement::TYPE_ROW]) ?
                            ArrayHelper::index($matrixElements[FoquzQuestionMatrixElement::TYPE_ROW], 'id') : [];
                        $matrixVariants = FoquzQuestionMatrixElementVariant::find()
                            ->where(['matrix_element_id' => ArrayHelper::getColumn($columns, 'id')])
                            ->all();
                        /** @var FoquzQuestionMatrixElementVariant[] $matrixVariants */
                        $matrixVariants = ArrayHelper::index($matrixVariants, 'id');
                        $hasRows = false;
                        foreach ($answer as $rowId => $row) {
                            $hasRows = true;
                            $rowItemContent = [];
                            $rowItemContent[] = [
                                'question'         => $rows[$rowId]->name ?? '',
                                'is_required'      => false,
                                'is_variant'       => true,
                                'is_3d_matrix'     => true,
                                'is_3d_matrix_row' => true,
                                'answers'          => [],
                            ];
                            $colItemContent = [];
                            $hasColumns = false;
                            foreach ($row as $colId => $column) {
                                $hasColumns = true;
                                $variantsContent = [];
                                foreach ($column as $variant) {
                                    if ($variant == null || $variant == -1) {
                                        continue;
                                    }
                                    $variantsContent[] = [
                                        'type'  => 'answer',
                                        'value' => $matrixVariants[$variant]->name ?? ''
                                    ];
                                }
                                if (empty($variantsContent) && isset($column[0]) && $column[0] == -1) {
                                    $variantsContent[] = ['type' => 'answer-skipped'];
                                } elseif (empty($variantsContent)) {
                                    $variantsContent[] = ['type' => 'answer-empty'];
                                }
                                $colItemContent[] = [
                                    'question'     => $columns[$colId]->name ?? '',
                                    'is_required'  => false,
                                    'is_variant'   => true,
                                    'is_3d_matrix' => true,
                                    'answers'      => $variantsContent,
                                ];
                            }
                            $itemContent = ArrayHelper::merge($itemContent, $rowItemContent);
                            $itemContent = ArrayHelper::merge($itemContent, $colItemContent);
                        }
                        $itemContent = ArrayHelper::merge([
                            [
                                'question'           => $questionName,
                                'type_name'          => $question->typeName,
                                'is_required'        => $question->is_required,
                                'is_variant'         => false,
                                'is_question_hidden' => $hiddenQuestion,
                                'answers'            => [],
                            ],
                        ], $itemContent);
                        break;
                }
                switch ($question->main_question_type) {
                    case FoquzQuestion::TYPE_VARIANT_STAR:
                    case FoquzQuestion::TYPE_NPS_RATING:
                    case FoquzQuestion::TYPE_SMILE_RATING:
                    case FoquzQuestion::TYPE_PRIORITY:
                    case FoquzQuestion::TYPE_FILIAL:
                    case FoquzQuestion::TYPE_GALLERY_RATING:
                    case FoquzQuestion::TYPE_SEM_DIFFERENTIAL:
                    case FoquzQuestion::TYPE_CHOOSE_MEDIA:
                    case FoquzQuestion::TYPE_SCALE:
                    case FoquzQuestion::TYPE_DICTIONARY:
                    case FoquzQuestion::TYPE_SIMPLE_MATRIX:
                    case FoquzQuestion::TYPE_3D_MATRIX:
                        $commentField = 'self_variant';
                        break;
                    case FoquzQuestion::TYPE_STAR_RATING:
                    case FoquzQuestion::TYPE_FILE_UPLOAD:
                    case FoquzQuestion::TYPE_RATING:
                    case FoquzQuestion::TYPE_ASSESSMENT:
                    case FoquzQuestion::TYPE_VARIANTS:
                        $commentField = 'answer';
                        break;
                    default:
                        $commentField = null;
                }
                if (
                    isset($commentField) &&
                    !empty($answerItem->$commentField) &&
                    $question->extra_question_type === FoquzQuestion::EXTRA_QUESTION_OFF
                ) {
                    $itemContent[] = [
                        'question'    => $question->comment_label ?: 'Комментарий',
                        'is_required' => false,
                        'is_variant'  => true,
                        'answers'     => [
                            [
                                'type'  => 'answer',
                                'value' => $answerItem->$commentField,
                            ],
                        ],
                    ];
                }
                if ($question->poll->point_system && $question->maxPoints !== null) {
                    $showPoints = true;
                    if ((int)$question->main_question_type === FoquzQuestion::TYPE_VARIANTS) {
                        $detailsWithoutPoints = FoquzQuestionDetail::find()
                            ->select('id')
                            ->where(['foquz_question_id' => $question->id])
                            ->andWhere(['without_points' => true])
                            ->column();
                        if (!empty($detailsWithoutPoints)) {
                            $answerDetails = $answerItem->detail_item;
                            if (is_string($answerDetails)) {
                                $answerDetails = trim($answerDetails, '"');
                                $answerDetails = str_replace('\"', '"', $answerDetails);
                                $answerDetails = json_decode($answerDetails, true) ?? [];
                            }
                            if (!empty($answerDetails) && count(array_diff($answerDetails,
                                    $detailsWithoutPoints)) === 0) {
                                $showPoints = false;
                            }
                        }
                    }
                    if ($showPoints) {
                        $itemContent[] = [
                            'type'  => 'points',
                            'value' => $answerItem->points,
                            'max'   => $question->maxPoints,
                        ];
                    }
                }
            }
            $content[] = ['id' => $i++, 'item' => $itemContent, 'type_id' => $question->main_question_type];
        }

        return $content;
    }

    public function getTextFieldContentForEmail($value, int $type, int $mask): array
    {
        $answers = [];
        if ($type === 1) {
            $rawAnswers = preg_split("/[\r\n|\n|\r]+/", $value);
            foreach ($rawAnswers as $rawAnswer) {
                $answers[] = [
                    'type'  => 'answer',
                    'value' => $rawAnswer,
                ];
            }
        } else {
            switch ($mask) {
                case FoquzQuestion::NO_MASK:
                case FoquzQuestion::MASK_PHONE:
                case FoquzQuestion::MASK_NUMBER:
                case FoquzQuestion::MASK_DATE:
                case FoquzQuestion::MASK_PERIOD:
                case FoquzQuestion::MASK_DATE_MONTH:
                    $answers[] = [
                        'type'  => 'answer',
                        'value' => $value,
                    ];
                    break;
                case FoquzQuestion::MASK_EMAIL:
                    $answers[] = [
                        'type'  => 'email',
                        'value' => $value,
                    ];
                    break;
                case FoquzQuestion::MASK_SITE:
                    $answers[] = [
                        'type'  => 'link',
                        'value' => $value,
                    ];
                    break;
                case FoquzQuestion::MASK_NAME:
                    if (is_string($value)) {
                        $value = json_decode($value);
                    }
                    $answers[] = [
                        'type'  => 'answer',
                        'value' => ($value->surname ?? '') . ' ' . ($value->name ?? '') . ' ' . ($value->patronymic ?? ''),
                    ];
                    break;
            }
        }
        return $answers;
    }

    /**
     * Возвращает ответы на вопросы (для вывода в разделе "Ответы" и при печати)
     * @return array
     */
    public function getAnswers()
    {
        $channels = Channel::find()->where(['poll_id' => $this->foquz_poll_id])->orderBy(['position' => SORT_ASC])->all();
        $notifications = [];
        foreach ($channels as $channel) {
            $notifications[] = [
                'channelName' => $channel->name,
                'active'      => $channel->active,
                'repeatCount' => $channel->getRepeatsCount(),
                'className'   => strtolower($channel->name),
            ];
        }
        $clientAdditionalFields = [];
        if ($this->contact && count($this->contact->additionalFieldValues) > 0) {
            foreach ($this->contact->additionalFieldValues as $afv) {
                $clientAdditionalFields[$afv->additional_field_id] = $afv->value;
            }
        }

        $ordersCount = $this->contact ? $this->contact->ordersCount : 0;
        if ($ordersCount && ($user = Yii::$app->user->identity) && ($user->isEditor() || $user->isWatcher())) {
            $editorFolders = FoquzPoll::getEditorFolderIds($user->id);
            if (!in_array($this->foquz_poll_id, $editorFolders['all'])) {
                $ordersCount = 0;
            }
        }
        if (substr($this->created_at, 0, 10) === substr($this->updated_at, 0, 10)) {
            $finished = date('H:i', strtotime($this->updated_at));
        } else {
            $finished = date('d.m.Y H:i', strtotime($this->updated_at));
        }

        $link = null;
        $allowEdit = false;
        /** @var User $user */
        $user = Yii::$app->user->identity;
        if (
            $user &&
            ($user->isAdmin() || $user->isEditor() || ($user->isExecutor() && $user->can_edit_answers) ||
                (
                    $user->isFilialEmployee() &&
                    (
                        empty($user->userFilials) ||
                        (
                            $this->answer_filial_id &&
                            in_array($this->answer_filial_id, ArrayHelper::getColumn($user->userFilials, 'filial_id'))
                        )
                    )
                ))
        ) {
            $allowEdit = true;
            $sends = $this->sends;
            /** @var FoquzPollMailingListSend $send */
            $send = end($sends);
            if ($send) {
                $link = \Yii::$app->params['protocol'] . '://' . $this->foquzPoll->company->alias . '/p/' . $send->key . '?edit=1&view=1';
            }
        }

        $hiddenQuestions = FoquzPollAnswerHiddenQuestion::find()
            ->select('question_id')
            ->where(['answer_item_id' => ArrayHelper::getColumn($this->foquzAnswer, 'id')])
            ->column();

        $pointPercent = $this->max_points > 0 ? round(($this->points / $this->max_points) * 100) : 0;
        if ($pointPercent < 0) {
            $pointPercent = 0;
        }

        return [
            'id'          => $this->id,
            'timeToPass'  => $this->foquzPoll->time_to_pass,
            'elapsedTime' => $this->elapsedTime,
            'pollName'    => $this->foquzPoll->name,
            'isAuto'      => $this->foquzPoll->is_auto,
            'orderId'     => $this->order->id ?? null,
            'complaint'   => $this->complaint ? [
                'text'      => $this->complaint->text,
                'photoUrls' => FoquzComplaintFile::find()->select(['file_path'])->where(['foquz_poll_answer_id' => $this->id])->column(),
            ] : null,
            'ordersCount' => $ordersCount,
            'contactId'   => $this->contact ? $this->contact->id : null,
            'clientName'  => $this->contact ? implode(' ',
                [$this->contact->last_name, $this->contact->first_name, $this->contact->patronymic]) : null,
            'clientPhone' => $this->contact->formattedPhone ?? null,
            'clientEmail' => $this->contact->email ?? null,

            'clientFilials'          => array_map(function ($item) {
                return $item->filial->toArray();
            }, $this->contact->contactFilials ?? []),
            'clientBirthday'         => $this->contact->birthday ?? null,
            'clientGender'           => $this->contact->gender ?? null,
            'clientTags'             => array_map(function ($item) {
                return $item->toArray();
            }, $this->contact->tags ?? []),
            'clientAdded'            => $this->contact ? date("Y-m-d", $this->contact->created_at) : null,
            'clientUpdated'          => $this->contact ? date("Y-m-d", $this->contact->updated_at) : null,
            'clientComputedFields'   => ($this->contact && $this->contact->computedFields) ? $this->contact->computedFields->toObject() : null,
            'clientAdditionalFields' => $clientAdditionalFields,
            'clientCustomerId'       => $this->contact ? $this->contact->company_client_id : null,
            'personalAgreement'      => (bool)$this->user_agreement,
            'answerCustomFields'     => json_decode($this->custom_fields) ?? null,
            'filialName'             => $this->order && $this->order->filial ? $this->order->filial->name : null,
            'notifications'          => $notifications,
            'answerChannel'          => $this->answerChannel,
            'sends'                  => $this->sends,
            'displaySettings'        => $this->foquzPoll->displaySetting,
            'displayPages'           => $this->foquzPoll->displayPages,
            'createdAt'              => date('d.m.Y H:i', $this->foquzPoll->created_at),
            'started'                => date('d.m.Y H:i', strtotime($this->created_at)),
            'finished'               => $finished,
            'answerTime'             => $this->answerTime(true),
            'updatedAt'              => date('d.m.Y H:i', strtotime($this->updated_at)),
            'processingUpdatedAt'    => $this->processing && $this->processing->updated_at !== null ? date("d.m.Y H:i",
                strtotime($this->processing->updated_at)) : date("d.m.Y H:i", strtotime($this->updated_at)),
            'processing'             => $this->processing,
            'questions'              => $this->collectQuestionsWithAnswerItems(false),
            'hiddenQuestions'        => $hiddenQuestions,
            'points'                 => $this->foquzPoll->point_system ? [
                'answer_points' => $this->points ?? 0,
                'points_max'    => $this->max_points,
                'percent'       => $pointPercent,
            ] : [],
            'device' => $this->device,
            'language' => $this->pollLang->name ?? null,
            'default_moderator_id' => $this->foquzPoll->default_moderator_id,
            'default_executor_id' => $this->foquzPoll->default_executor_id,
            'dictionary_id' => $this->foquzPoll->dictionary_id ?? null,
            'link' => $link,
            'allowEdit' => $allowEdit,
            'answer_tags' => $this->tags,
        ];
    }

    public function collectQuestionsWithAnswerItems($sortDeletedQuestions = true)
    {
        $questions = [];
        $q = $this->foquzPoll->getFoquzQuestions()->with("semDifSetting")->where(['is_tmp' => 0])->andWhere([
            '!=',
            'main_question_type',
            FoquzQuestion::TYPE_INTERMEDIATE_BLOCK
        ]);
        if (!$sortDeletedQuestions) {
            $q->orderBy("position asc");
        }

        /** @var FoquzQuestion[] $allQuestions */
        $allQuestions = $q->all();
        $deletedQuestion = array_filter($allQuestions, static function ($question) {
            return $question->is_deleted;
        });

        if (!$sortDeletedQuestions && !empty($deletedQuestion)) {
            $questionsWithAnswer = FoquzPollAnswerItem::find()
                ->select('foquz_question_id')
                ->where(['foquz_question_id' => ArrayHelper::getColumn($allQuestions, 'id')])
                ->distinct()
                ->column();

            foreach ($allQuestions as $key => $question) {
                if ($question->is_deleted && !in_array($question->id, $questionsWithAnswer)) {
                    unset($allQuestions[$key]);
                }
            }

            $allQuestions = array_values($allQuestions);
        }

        foreach ($allQuestions as $idx => $question) {

            if (!empty($question->service_name)) {
                $shortName = $question->service_name;
            } elseif (!empty($question->name)) {
                $shortName = $question->name;
            } else {
                $shortName = $question->description;
            }

            $matrixElements = ArrayHelper::index($question->activeMatrixElements, null, 'type_id');
            if ($question->donor_columns) {
                $donorAnswerItem = FoquzPollAnswerItem::findOne([
                    'foquz_poll_answer_id' => $this->id,
                    'foquz_question_id'    => $question->donor_columns,
                ]);
                if ($donorAnswerItem) {
                    $selectedIDs = $donorAnswerItem->detail_item;
                    if (is_string($selectedIDs)) {
                        $selectedIDs = json_decode($selectedIDs, true);
                    }
                    if ($donorAnswerItem->is_self_variant) {
                        $selectedIDs[] = -1;
                    }
                    if (!empty($matrixElements[FoquzQuestionMatrixElement::TYPE_COLUMN]) && $selectedIDs !== null) {
                        /**
                         * @var int $key
                         * @var  FoquzQuestionMatrixElement $matrixElement
                         */
                        foreach ($matrixElements[FoquzQuestionMatrixElement::TYPE_COLUMN] as $key => $matrixElement) {
                            $donorID = null;
                            if (
                                $question->columnsDonor->main_question_type === FoquzQuestion::TYPE_DICTIONARY &&
                                $matrixElement->donor_dictionary_element_id
                            ) {
                                $donorID = $matrixElement->donor_dictionary_element_id;
                            } elseif ($matrixElement->donor_variant_id) {
                                $donorID = $matrixElement->donor_variant_id;
                            } elseif (
                                !$matrixElement->donor_dictionary_element_id &&
                                !$matrixElement->donor_variant_id &&
                                $donorAnswerItem->is_self_variant
                            ) {
                                $donorID = -1;
                            }

                            if (!in_array($donorID, $selectedIDs)) {
                                $matrixElements[FoquzQuestionMatrixElement::TYPE_COLUMN][$key]->isChecked = false;
                            }
                        }
                    }
                }
            }
            if ($question->donor_rows) {
                $donorAnswerItem = FoquzPollAnswerItem::findOne([
                    'foquz_poll_answer_id' => $this->id,
                    'foquz_question_id'    => $question->donor_rows,
                ]);
                if ($donorAnswerItem) {
                    $selectedIDs = $donorAnswerItem->detail_item;
                    if (is_string($selectedIDs)) {
                        $selectedIDs = json_decode($selectedIDs, true);
                    }
                    if ($donorAnswerItem->is_self_variant) {
                        $selectedIDs[] = -1;
                    }
                    if (!empty($matrixElements[FoquzQuestionMatrixElement::TYPE_ROW]) && $selectedIDs !== null) {
                        /**
                         * @var int $key
                         * @var  FoquzQuestionMatrixElement $matrixElement
                         */
                        foreach ($matrixElements[FoquzQuestionMatrixElement::TYPE_ROW] as $key => $matrixElement) {
                            $donorID = null;
                            if (
                                $question->rowsDonor->main_question_type === FoquzQuestion::TYPE_DICTIONARY &&
                                $matrixElement->donor_dictionary_element_id
                            ) {
                                $donorID = $matrixElement->donor_dictionary_element_id;
                            } elseif ($matrixElement->donor_variant_id) {
                                $donorID = $matrixElement->donor_variant_id;
                            } elseif (
                                !$matrixElement->donor_dictionary_element_id &&
                                !$matrixElement->donor_variant_id &&
                                $donorAnswerItem->is_self_variant
                            ) {
                                $donorID = -1;
                            }

                            if (!in_array($donorID, $selectedIDs)) {
                                $matrixElements[FoquzQuestionMatrixElement::TYPE_ROW][$key]->isChecked = false;
                            }
                        }
                    }
                }
            }
            $matrixElements = [
                'columns' => !empty($matrixElements[FoquzQuestionMatrixElement::TYPE_COLUMN]) ?
                    $matrixElements[FoquzQuestionMatrixElement::TYPE_COLUMN] : [],
                'rows'    => !empty($matrixElements[FoquzQuestionMatrixElement::TYPE_ROW]) ?
                    $matrixElements[FoquzQuestionMatrixElement::TYPE_ROW] : [],
            ];

            $maskConfig = [];
            foreach (json_decode($question->mask_config) ?? [] as $key => $mask) {
                $maskConfig[$key] = [
                    'minLength'       => isset($mask->minLength) ? (int)$mask->minLength : null,
                    'maxLength'       => isset($mask->maxLength) ? (int)$mask->maxLength : null,
                    'placeholderText' => isset($mask->placeholderText) ? (string)$mask->placeholderText : null,
                    'required'        => isset($mask->required) && $mask->required === 'true',
                    'visible'         => isset($mask->visible) && $mask->visible === 'true',
                ];
            }

            $recipients = [];
            if (in_array($question->main_question_type, [FoquzQuestion::TYPE_VARIANTS, FoquzQuestion::TYPE_DICTIONARY],
                true)) {
                $indexes = array_flip(ArrayHelper::getColumn($allQuestions, 'id'));
                $recipients = FoquzQuestion::getRecipientsRecursive($allQuestions, $question->id);
                $recipients = array_map(static function ($recipient) use ($indexes) {
                    return [
                        'number' => $indexes[$recipient->id] + 1,
                        'name'   => $recipient->description,
                    ];
                }, $recipients);
            }

            $answerItemQuestion = $this->collectAnswerItem($question->id);
            $questionMaxPoints = null;
            if ($question->main_question_type==FoquzQuestion::TYPE_VARIANTS && isset($answerItemQuestion['max_points']) && !is_null($answerItemQuestion['max_points'])) {
                $questionMaxPoints  = $answerItemQuestion['max_points'];
            }

            /** @var FoquzQuestion $question */
            $questions[$idx] = [
                'assessmentType'             => $question->rating_type,
             //   'pointName'                  => $question->point ? $question->point->name : null,
                'clarifyingQuestion'         => $question->detail_question != '' ? $question->detail_question : null,
                'employee'                   => $question->getEmployeeName($this->id),
                'id'                         => $question->id,
                'mediaUrls'                  => $question->grabMediaContent(),
                'isSelfAnswer'               => (bool)$question->is_self_answer,
                'isRequired'                 => (bool)$question->is_required,
                'mediaType'                  => $question->type,
                'name'                       => !$question->name ? $question->description : implode('. ',
                    [trim($question->name), trim($question->description)]),
                'questionName'               => $question->name,
                'description'                => $question->description,
                'shortName'                  => $shortName,
                'text'                       => $question->text,
                'type'                       => $question->main_question_type,
                'variants'                   => $question->collectVariants(),
                'forAllRates'                => $question->for_all_rates,
                'answer'                     => $answerItemQuestion,
                'linkWithClientField'        => $question->link_with_client_field,
                'linkedClientField'          => ContactAdditionalField::getText($question->linked_client_field),
                'chooseType'                 => $question->choose_type,
                'chooseMedia'                => $question->grabChooseContent(),
                'gallery'                    => $question->grabMediaContent(),
                'smileType'                  => $question->smile_type,
                'smiles'                     => $question->questionSmiles,
                'npsRating'                  => $question->npsRatingSetting ?? null,
                'scaleRatingSetting'         => $question->scaleRatingSetting ?? null,
                'matrixSettings'             => json_decode($question->matrix_settings),
                'differentialRows'           => $question->differentialRows,
                'starRatingOptions'          => $question->starRatingOptions,
                'maxPoints'                  => !is_null($questionMaxPoints) ? $questionMaxPoints : $question->maxPoints,
                //'maxPoints'                  => $question->maxPoints,
                'self_variant_text'          => $question->self_variant_text,
                'rightAnswer'                => $question->rightAnswer ?? null,
                'variantsType'               => $question->variants_element_type,
                'onlyDateMonth'              => $question->only_date_month,
                'date_type'                  => $question->date_type,
                'skip'                       => $question->skip,
                'skipVariant'                => $question->skip_variant,
                'skipText'                   => $question->skip_text,
                'set_variants'               => $question->set_variants,
                'isDeleted'                  => $question->is_deleted,
                'semDifSetting'              => $question->semDifSetting ? $question->semDifSetting->attributes : null,
                'donor'                      => $question->donor,
                'donor_rows'                 => $question->donor_rows,
                'donor_columns'              => $question->donor_columns,
                'donor_chosen'               => $question->donor_chosen,
                'donor_cols_chosen'          => $question->donor_cols_chosen,
                'sub_description'            => $question->clearHTML($question->sub_description),
                'dictionary_id'              => $question->dictionary_id,
                'dictionary_element_id'      => $question->dictionary_element_id,
                'dictionary_list_type'       => $question->dictionary_list_type,
                'dictionary_sort'            => $question->dictionary_sort,
                'matrixElements'             => $matrixElements,
                'mask_type'                  => $question->mask,
                'arRegionsIDs'               => in_array($question->main_question_type,
                    [FoquzQuestion::TYPE_ADDRESS, FoquzQuestion::TYPE_FORM],
                    true) && $question->addressCodes ? json_decode($question->addressCodes->regions) : [],
                'arDistrictsIDs'             => in_array($question->main_question_type,
                    [FoquzQuestion::TYPE_ADDRESS, FoquzQuestion::TYPE_FORM],
                    true) && $question->addressCodes ? json_decode($question->addressCodes->districts) : [],
                'arCityIDs'                  => in_array($question->main_question_type,
                    [FoquzQuestion::TYPE_ADDRESS, FoquzQuestion::TYPE_FORM],
                    true) && $question->addressCodes ? json_decode($question->addressCodes->cities) : [],
                'arStreetsIDs'               => in_array($question->main_question_type,
                    [FoquzQuestion::TYPE_ADDRESS, FoquzQuestion::TYPE_FORM],
                    true) && $question->addressCodes ? json_decode($question->addressCodes->streets) : [],
                'formFields'                 => $question->getQuizzes(),
                'maskConfig'                 => $maskConfig,
                'fromOne'                    => $question->from_one,
                'commentEnabled'             => $question->comment_enabled,
                'commentRequired'            => $question->comment_required,
                'commentLabel'               => $question->comment_label,
                'selfVariantNothing'         => $question->self_variant_nothing,
                'selfVariantDescription'     => $question->self_variant_description,
                'selfVariantCommentRequired' => $question->self_variant_comment_required,
                'recipients' => $recipients,
                'fileTypes' => $question->file_types,
                'filesLength' => $question->files_length,
                'extraRequired' => $question->extra_required,
                'min_choose_variants' => $question->min_choose_variants,
                'max_choose_variants' => $question->max_choose_variants,
                'extra_question_type' => $question->extra_question_type,
                'extra_question_rate_from' => $question->extra_question_rate_from,
                'extra_question_rate_to' => $question->extra_question_rate_to,
                'self_variant_file' => $question->selfVariantFile,
                'min_choose_extra_variants' => $question->min_choose_extra_variants,
                'max_choose_extra_variants' => $question->max_choose_extra_variants,
                'cardSortingCategories' => $question->cardSortingCategories,
            ];

            if ($question->main_question_type === FoquzQuestion::TYPE_FIRST_CLICK) {
                $questions[$idx]['firstClickArea'] = $question->firstClickArea;
            }
        }
        return $questions;
    }

    public function collectAnswerItem($question_id)
    {


        $question = FoquzQuestion::findOne($question_id);
        /** @var FoquzPollAnswerItem $answerItem */
        $answerItem = FoquzPollAnswerItem::find()->where([
            'foquz_poll_answer_id' => $this->id,
            'foquz_question_id'    => $question_id,
        ])->one();

        if (!$answerItem || $answerItem->isEmpty) {
            Yii::info("Answer item not found or empty for question {$question_id} and answer {$this->id} (isEmpty: " . ($answerItem->isEmpty ?? 'null') . ')');
            return null;
        }


        $answerItemArray = [];
        $answerItemArray['skipped'] = $answerItem->skipped;
        if (in_array($question->main_question_type, [
            FoquzQuestion::TYPE_ASSESSMENT,
            FoquzQuestion::TYPE_RATING,
            FoquzQuestion::TYPE_STAR_RATING,
            FoquzQuestion::TYPE_VARIANT_STAR
        ])) {

            if (!$answerItem->skipped) {
                $answerItemArray['rating'] = $answerItem->rating;
            }
            if ($question->rating_type === FoquzQuestion::RATING_DISHES) {
                $dishRatings = [];
                foreach ($this->getFoquzPollDishes()->orderBy('score desc')->all() as $dishScore) {
                    if ($this->order) {
                        $dishOrderData = $this->order->getDishes()->where(['dish_id' => $dishScore->dish_id])->one();
                        $dishRatings[] = [
                            'dish'     => ['name' => $dishScore->dish->name],
                            'quantity' => $dishOrderData->quantity ?? 0,
                            'value'    => $dishScore->score,
                        ];
                    }
                }
                $answerItemArray['dishRatings'] = $dishRatings;
            }
            if ($question->rating_type === FoquzQuestion::RATING_OPTIONS || $question->detail_question != '') {
                $selected = is_array($answerItem->detail_item) ? $answerItem->detail_item : json_decode($answerItem->detail_item);
                $answerItemArray['selectedIds'] = $selected && is_array($selected) ? array_values($selected) : [];
                $answerItemArray['selfVariant'] = $answerItem->self_variant != "" ? $answerItem->self_variant : null;
            }
            if (!$answerItem->skipped) {
                if (in_array($question->main_question_type,
                    [FoquzQuestion::TYPE_VARIANT_STAR, FoquzQuestion::TYPE_PRIORITY])) {
                    $answerItemArray['comment'] = $answerItem->self_variant;
                } else {
                    $answerItemArray['comment'] = $answerItem->answer != "" ? $answerItem->answer : null;
                }
            }
        }
        if ($question->main_question_type === FoquzQuestion::TYPE_DATE) {
            $decoded_date = json_decode($answerItem->answer);
            if ($decoded_date !== null) {
                $formattedDate = '';
                switch ($question->date_type) {
                    case 0: // только дата
                        if ($question->only_date_month) { // только день и месяц
                            $formattedDate = DateTimeHelper::shortDateString2dateWithMonthAsWord($decoded_date->date);
                        } else { // день.месяц.год
                            $formattedDate = $decoded_date->date;
                        }
                        break;
                    case 1: // только время
                        $formattedDate = $decoded_date->time;
                        break;
                    case 2: // дата и время
                        if ($question->only_date_month) { // день месяц и время
                            $formattedDate = DateTimeHelper::shortDateString2dateWithMonthAsWord($decoded_date->date) . ' ' . $decoded_date->time;
                        } else { // день месяц год время
                            $formattedDate = $decoded_date->date . ' ' . $decoded_date->time;
                        }
                        break;
                }
                $answerItemArray['answer'] = $formattedDate;

                if ($question->poll->point_system && $question->rightAnswer) {
                    $dateRight = date('Y-m-d',
                            strtotime($decoded_date->date)) === $question->rightAnswer->decodedAnswer->date || $question->rightAnswer->decodedAnswer->date === '' || $decoded_date->date === $question->rightAnswer->decodedAnswer->date;
                    $timeRight = str_replace(' ', '', $decoded_date->time) === str_replace(' ', '',
                            $question->rightAnswer->decodedAnswer->time) || str_replace(' ', '',
                            $question->rightAnswer->decodedAnswer->time) === '__:__' || $decoded_date->time === $question->rightAnswer->decodedAnswer->time;
                    $answerItemArray['rightAnswer'] = $question->rightAnswer !== null && $dateRight && $timeRight;

                    if (!$answerItemArray['rightAnswer'] && $question->rightAnswer->points) {
                        $formattedDate = '';
                        switch ($question->date_type) {
                            case 0: // только дата
                                if ($question->only_date_month) { // только день и месяц
                                    $formattedDate = DateTimeHelper::shortDateString2dateWithMonthAsWord($question->rightAnswer->decodedAnswer->date);
                                } else { // день.месяц.год
                                    $formattedDate = $question->rightAnswer->decodedAnswer->dateFormat;
                                }
                                break;
                            case 1: // только время
                                $formattedDate = $question->rightAnswer->decodedAnswer->time;
                                break;
                            case 2: // дата и время
                                if ($question->only_date_month) { // день месяц и время
                                    $formattedDate = DateTimeHelper::shortDateString2dateWithMonthAsWord($question->rightAnswer->decodedAnswer->date) . ' ' . $question->rightAnswer->decodedAnswer->time;
                                } else { // день месяц год время
                                    $formattedDate = $question->rightAnswer->decodedAnswer->dateFormat . ' ' . $question->rightAnswer->decodedAnswer->time;
                                }
                                break;
                        }
                        $answerItemArray['correct_answer'] = ['points' => $question->rightAnswer->points, 'text' => $formattedDate];
                    }
                }
            } else {
                $answerItemArray['answer'] = '';
            }
        } elseif (in_array($question->main_question_type,
            [FoquzQuestion::TYPE_VARIANTS, FoquzQuestion::TYPE_FILIAL, FoquzQuestion::TYPE_DICTIONARY])) {
            $selected = is_array($answerItem->detail_item) ? $answerItem->detail_item : json_decode($answerItem->detail_item);
            $answerItemArray['selectedIds'] = $selected ?: [];
            $answerItemArray['selfVariant'] = $answerItem->self_variant != "" ? $answerItem->self_variant : null;
            if ($question->donor) {
                if (array_search("-1", $answerItemArray['selectedIds'])) {
                    $donorAnswerItem = FoquzPollAnswerItem::findOne([
                        'foquz_poll_answer_id' => $this->id,
                        'foquz_question_id' => $question->donor,
                    ]);
                    if ($donorAnswerItem) {
                        $answerItemArray['selfVariant'] = $donorAnswerItem->self_variant;
                    }
                }
            }
            $answerItemArray['comment'] = $answerItem->answer ?: null;
        } elseif (in_array($question->main_question_type,
            [FoquzQuestion::TYPE_TEXT_ANSWER, FoquzQuestion::TYPE_ADDRESS])) {
            if ($question->mask === FoquzQuestion::MASK_NAME) {
                $answerItemArray['answer'] = $answerItem->answer != "" ? json_decode($answerItem->answer) : null;
            } elseif ($question->mask === FoquzQuestion::MASK_PERIOD) {
                $answerItemArray['answer'] = $answerItem->answer != "" ? str_replace(' - ', '—',
                    $answerItem->answer) : null;
            } elseif ($question->mask === FoquzQuestion::MASK_DATE_MONTH) {
                $answerItemArray['answer'] = null;
                if ($answerItem->answer != "") {
                    $answerItemArray['answer'] = DateTimeHelper::shortDateString2dateWithMonthAsWord($answerItem->answer);
                }

            } else {
                $answerItemArray['answer'] = $answerItem->answer != "" ? $answerItem->answer : null;
            }
        } elseif ($question->main_question_type === FoquzQuestion::TYPE_FILE_UPLOAD) {
            $files = [];

            foreach ($answerItem->answerItemFiles as $file) {
                $files[] = [
                    'id'          => $file->id,
                    'name'        => $file->origin_name,
                    'url'         => $file->file_path,
                    'preview_url' => $file->preview,
                ];
            }
            $result = [
                'files'    => $files,
                'passedAt' => date('d.m.Y', strtotime($answerItem->created_at)),
                'comment'  => $answerItem->answer != "" ? $answerItem->answer : null,
            ];
            $answerItemArray['answer'] = (object)$result;
        } elseif ($question->main_question_type === FoquzQuestion::TYPE_FORM) {
            $values = [];
            $dbAnswer = json_decode($answerItem->answer);
            $properties = $question->getFormFields()->select('id, name, link_with_client_field, linked_client_field, mask_type')->all();
            foreach ($properties as $property) {
                $id = $property->id;
                if (isset($dbAnswer->$id) && $dbAnswer->$id !== '') {
                    if ($property->mask_type === FoquzQuestion::MASK_PERIOD) {
                        $value = str_replace(' - ', '—', $dbAnswer->$id);
                    } else if ($property->mask_type === FoquzQuestion::MASK_DATE_MONTH) {
                        $value = DateTimeHelper::shortDateString2dateWithMonthAsWord($dbAnswer->$id);
                    } else {
                        $value = $dbAnswer->$id;
                    }
                    $values[] = [
                        'id'                  => $property->id,
                        'label'               => $property->name,
                        'value'               => $value,
                        'linkWithClientField' => $property->link_with_client_field,
                        'linkedClientField'   => ContactAdditionalField::getText($property->linked_client_field),
                    ];
                }
            }
            $result['values'] = $values;
            $answerItemArray['answer'] = (object)$result;
        } elseif ($question->main_question_type === FoquzQuestion::TYPE_PRIORITY) {
            $variants = $answerItem->detail_item;
            $variants = is_array($variants) ? $variants : json_decode($variants);
            $answerItemArray['answer'] = [
                'variants' => $variants ?: json_decode($answerItem->answer),
                'comment'  => $answerItem->self_variant,
            ];
            if ($question->poll->point_system) {
                if (!$variants) {
                    $answerItemArray['rightAnswer'] = $question->rightAnswer && json_decode($answerItem->answer) === $question->rightAnswer->decodedAnswer;
                } else {
                    $variantsText = [];
                    if (!$question->getMainDonor() || $question->getMainDonor()->main_question_type !== FoquzQuestion::TYPE_DICTIONARY) {
                        $questionID = $question->donor ? $question->getMainDonor()->id : $question->id;
                        $details = FoquzQuestionDetail::find()->where(['foquz_question_id' => $questionID])->orderBy('position asc')->all();
                        /** @var FoquzQuestionDetail[] $details */
                        $details = ArrayHelper::index($details, 'id');
                        foreach ($variants as $variant) {
                            if (isset($details[$variant])) {
                                $variantsText[] = $details[$variant]->question;
                            }
                        }
                    } else {
                        $details = DictionaryElement::findAll(array_values($variants));
                        /** @var DictionaryElement[] $details */
                        $details = ArrayHelper::index($details, 'id');
                        foreach ($variants as $variant) {
                            if (isset($details[$variant])) {
                                $variantsText[] = $details[$variant]->fullPath;
                            }
                        }
                    }
                    $answerItemArray['rightAnswer'] = $question->rightAnswer && $variantsText === $question->rightAnswer->decodedAnswer;
                }
            }
        } elseif ($question->main_question_type === FoquzQuestion::TYPE_CHOOSE_MEDIA) {
            $answerItemArray['answer'] = [
                'answer'  => json_decode($answerItem->answer),
                'comment' => $answerItem->self_variant,
            ];
        } elseif ($question->main_question_type === FoquzQuestion::TYPE_GALLERY_RATING) {
            $answerItemArray['answer'] = [
                'answer'  => json_decode($answerItem->answer),
                'comment' => $answerItem->self_variant,
            ];
        } elseif ($question->main_question_type === FoquzQuestion::TYPE_SMILE_RATING) {

            $answerItemArray['answer'] = [
                'answer'  => $answerItem->answer,
                'rating'  => $answerItem->rating,
                'smile'   => FoquzQuestionSmile::findOne($answerItem->answer),
                'comment' => $answerItem->self_variant,
            ];

            if ($question->detail_question != '') {
                $selected = is_array($answerItem->detail_item) ? $answerItem->detail_item : json_decode($answerItem->detail_item);
                $answerItemArray['selectedIds'] = [];
                if (is_iterable($selected)) {
                    foreach ($selected as $selectedKey => $selectedItem) {

                        if ($selectedKey === 'self_variant') {
                            $answerItemArray['selfVariant'] = $selectedItem;
                        } else if ($selectedKey === 'text_answer') {
                            $answerItemArray['comment'] = $selectedItem;
                        } else {
                            if (is_array($selectedItem) && isset($selectedItem['self_variant'])) {
                                $answerItemArray['selfVariant'] = $selectedItem['self_variant'];
                            } else {
                                $answerItemArray['selectedIds'][] = $selectedItem;
                            }
                        }
                    }
                }
            }

        } elseif ($question->main_question_type === FoquzQuestion::TYPE_NPS_RATING) {
            $answerItemArray['answer'] = [
                'rating'  => $answerItem->rating,
                'comment' => $answerItem->self_variant,
            ];
            if ($question->isNpsWithVariants()) {
                $answerItems = json_decode($answerItem->answer, true);
                $answerItemArray['answer'] = $answerItems;
                $answerItemArray['rating'] = $answerItems ? array_sum($answerItems) / count($answerItems) : null;
                switch ($question->extra_question_type) {
                    case FoquzQuestion::EXTRA_QUESTION_OFF:
                        $answerItemArray['comment'] = $answerItem->self_variant;
                        break;
                    case FoquzQuestion::EXTRA_QUESTION_COMMON:
                        if (in_array($question->variants_element_type,
                            [FoquzQuestion::VARIANT_ELEMENT_TYPE_RADIO, FoquzQuestion::VARIANT_ELEMENT_TYPE_CHECKBOX],
                            true)) {
                            $answerItemArray['detail_item'] = is_string($answerItem->detail_item) ? json_decode($answerItem->detail_item,
                                true) : $answerItem->detail_item;
                            $answerItemArray['self_variant'] = $answerItem->self_variant;
                        } else {
                            $answerItemArray['text_answer'] = $answerItem->self_variant;
                        }
                        break;
                    case FoquzQuestion::EXTRA_QUESTION_COMMON_FOR_EACH:
                    case FoquzQuestion::EXTRA_QUESTION_DIFFERENT_EACH:
                        $answerItemArray['detail_item'] = is_string($answerItem->detail_item) ? json_decode($answerItem->detail_item,
                            true) : $answerItem->detail_item;
                        break;
                }
            }

        } elseif ($question->main_question_type === FoquzQuestion::TYPE_SCALE) {
            $answerItemArray['answer'] = [
                'rating'  => $answerItem->rating,
                'comment' => $answerItem->self_variant,
            ];
            if ($question->isScaleWithVariants()) {
                $answerItemArray['comment'] = $answerItem->self_variant ?? null;
                $answerItems = json_decode($answerItem->answer, true);
                $answerItemArray['answer'] = $answerItems;
                $answerItemArray['rating'] = $answerItems ? array_sum($answerItems) / count($answerItems) : null;
            }
        } elseif ($question->main_question_type === FoquzQuestion::TYPE_DISTRIBUTION_SCALE) {
            $answerItemArray['comment'] = $answerItem->self_variant ?? null;
            $answerItems = json_decode($answerItem->answer, true);
            $answerItemArray['answer'] = $answerItems;
            $answerItemArray['rating'] = $answerItems ? array_sum($answerItems) / count($answerItems) : null;
        } elseif (
            $question->main_question_type === FoquzQuestion::TYPE_SIMPLE_MATRIX ||
            $question->main_question_type === FoquzQuestion::TYPE_SEM_DIFFERENTIAL ||
            $question->main_question_type === FoquzQuestion::TYPE_VARIANT_STAR ||
            $question->main_question_type === FoquzQuestion::TYPE_3D_MATRIX) {
            $ans = json_decode($answerItem->answer);
            if ($question->donor && $question->main_question_type === FoquzQuestion::TYPE_SIMPLE_MATRIX) {
                $correctAns = [];
                foreach ($ans as $donorQuestionId => $selectedVars) {
                    if ($donorQuestionId == '-1') {
                        $donorQuestionId = $question->getMainDonor()->self_variant_text;
                    } elseif ($question->getMainDonor()->main_question_type === FoquzQuestion::TYPE_DICTIONARY) {
                        $donorQuestionId = DictionaryElement::findOne($donorQuestionId)->fullPath ?? null;
                    } else {
                        $detailItem = FoquzQuestionDetail::findOne($donorQuestionId);
                        if ($detailItem) {
                            $donorQuestionId = $detailItem->question;
                        }
                    }
                    $correctAns[$donorQuestionId] = $selectedVars;
                }
                $answerItemArray['answer'] = [
                    'answer'  => $correctAns,
                    'comment' => $answerItem->self_variant,
                ];
            } else {
                $answerItemArray['answer'] = [
                    'answer'  => $ans,
                    'comment' => $answerItem->self_variant,
                ];
            }
        }
        if ($question->main_question_type === FoquzQuestion::TYPE_VARIANT_STAR) {
            if ($answerItems = json_decode($answerItem->answer, true)) {
                unset($answerItems['extra']);
                $answerItemArray['answer']['rating'] = $answerItemArray['rating'] = array_sum($answerItems) / count($answerItems);
            }
        }
        if ($question->main_question_type === FoquzQuestion::TYPE_SIMPLE_MATRIX) {
            $answerItemArray['answer']['extra'] = $answerItem->detail_item;
        }
        if ($question->main_question_type === FoquzQuestion::TYPE_CARD_SORTING_CLOSED) {
            $answerItemArray['answer'] = ($answerItem->answer !== '[]') ? json_decode($answerItem->answer): null;
            $answerItemArray['comment'] = $answerItem->self_variant;
        }
        if ($question->main_question_type === FoquzQuestion::TYPE_FIRST_CLICK) {
            $answerItemArray['points'] = FoquzQuestionFirstClick::parseAnswer($answerItem->detail_item ?? '');
        }

        // Point systemE
        if ($question->poll->point_system && in_array($question->main_question_type, [
                FoquzQuestion::TYPE_VARIANTS,
                FoquzQuestion::TYPE_DATE,
                FoquzQuestion::TYPE_PRIORITY,
                FoquzQuestion::TYPE_CHOOSE_MEDIA,
                FoquzQuestion::TYPE_SIMPLE_MATRIX,
            ])
        ) {
            $answerItemArray['points'] = $this->calculatePointsForQuestion($question, $answerItem);
            //$answerItemArray['max_points'] = is_null($answerItem->max_points) ? $question->maxPoints : $answerItem->max_points;
            $answerItemArray['max_points'] = $question->maxPoints;



            if ($question->main_question_type === FoquzQuestion::TYPE_VARIANTS) {
                $answerDetails = $answerItem->detail_item;
                if (is_string($answerDetails)) {
                    $answerDetails = json_decode($answerDetails) ?? [];
                }
                if (is_object($answerDetails)) {
                    $answerDetails = (array)$answerDetails;
                    if ($key = array_search('is_self_answer', $answerDetails)) {
                        unset($answerDetails[$key]);
                    }
                }
                $correctAnswer = $answerItem->getCorrectAnswerForVariants();
                if (count($correctAnswer) > 0) {
                    $answerItemArray['correct_answer'] = $correctAnswer;
                }
                //if ($_SERVER["REMOTE_ADDR"]=="*************") {
                $answerItemArray['max_points'] = is_null($answerItem->max_points) ? $question->maxPoints : $answerItem->max_points;
                if ($answerItemArray['max_points']==0) {
                    //$answerItemArray['correct_answer']  = null;
                }
                //}

            } elseif ($question->main_question_type === FoquzQuestion::TYPE_SIMPLE_MATRIX && $question->donor && $question->max_points_calc_recipient) {
                $answerItemArray['max_points'] = $answerItem->max_points;
            } elseif ($question->main_question_type === FoquzQuestion::TYPE_CHOOSE_MEDIA) {
                $correctAnswer = $answerItem->getCorrectAnswerForMedia();
                if (count($correctAnswer) > 0) {
                    $answerItemArray['correct_answer'] = $correctAnswer;
                }
            } elseif ($question->main_question_type === FoquzQuestion::TYPE_SIMPLE_MATRIX) {
                $matrixSettings = json_decode($question->matrix_settings);
                $correctAnswer = $answerItem->getCorrectAnswerForSimpleMatrix($matrixSettings, $question->getMainDonor());
                if (count($correctAnswer) > 0) {
                    $answerItemArray['correct_answer'] = $correctAnswer;
                }
            }
        }

        return $answerItemArray;
    }

    public function getWithoutPoints()
    {
        $withoutPoints = false;
        /** @var FoquzPollAnswerItem[] $answerItems */
        $answerItems = ArrayHelper::index($this->foquzAnswer, 'foquz_question_id');
        foreach ($this->foquzPoll->foquzQuestions as $question) {
            if ($question->main_question_type !== FoquzQuestion::TYPE_VARIANTS && !empty($answerItems[$question->id])) {
                return false;
            }
            $questionDetails = ArrayHelper::index($question->questionDetails, null, 'without_points');
            if (empty($questionDetails[1]) && !empty($answerItems[$question->id]->detail_item)) {
                return false;
            }
            if (!empty($questionDetails[1]) && !empty($answerItems[$question->id]->detail_item)) {
                $answerItem = $answerItems[$question->id];
                $answerDetails = $answerItem->detail_item;
                if (is_string($answerDetails)) {
                    $answerDetails = json_decode($answerDetails) ?? [];
                }
                if (!empty($answerDetails) && count(array_diff($answerDetails,
                        ArrayHelper::getColumn($questionDetails[1], 'id'))) === 0) {
                    $withoutPoints = true;
                } elseif (!empty($answerDetails)) {
                    return false;
                }
            }
        }
        return $withoutPoints;
    }

    /**
     * Постановка задач на отправку вебхуков
     * @return void
     */
    public function pushWebhookJob($edit = false): void
    {
        $webhooks = $this->foquzPoll->answerWebhooks;
        if (empty($webhooks)) {
            return;
        }
        if ($this->foquz_poll_id == 320134 && $this->status !== self::STATUS_DONE) {
            return;
        }
        /** @var yii\queue\amqp_interop\Queue $queue */
        $queue = Yii::$app->rabbit_queue;
        foreach ($webhooks as $webhook) {
            if ($this->status === self::STATUS_DONE && !$edit) {
                $delay = 0;
            } elseif (empty($webhook->params['delay'])) {
                $delay = 15 * 60;
            } else {
                $delay = $webhook->params['delay'] * 60;
            }
            Yii::info('Pushing webhook job for answer ' . $this->id . ' with delay ' . $delay);

            $queue->delay($delay)->push(new SendWebhookJob([
                'type'       => Webhook::TYPE_NEW_ANSWER,
                'model_id'   => $this->id,
                'webhook_id' => $webhook->id,
                'params'     => [],
            ]));
        }
    }

    public function setUserDevice(): void
    {
        if (!empty($_SERVER['HTTP_USER_AGENT'])) {
            $parser = new UserAgentParser();
            $ua = $parser->parse();
            $this->os = $ua->platform();
            $this->useragent = $ua->browser() . ' ' . $ua->browserVersion();
        } else {
            $this->os = null;
            $this->useragent = null;
        }

        $this->ip_address = $_SERVER['REMOTE_ADDR'] ?? null;
        if (\Yii::$app->devicedetect->isTablet()) {
            $this->device = 'tablet';
        } elseif (\Yii::$app->devicedetect->isMobile()) {
            $this->device = 'mobile';
        } else {
            $this->device = 'desktop';
        }
    }


    /**
     * Удаление тегов анкеты
     * @param array $tags
     * @return void
     * @throws \Throwable
     */
    public function removeTags(array $tags) {
        self::getDb()->transaction(function($db) use ($tags) {
            foreach (FoquzCompanyTag::find()->where(['company_id' => $this->foquzPoll->company_id])
                         ->andWhere(['in', 'id', $tags])->asArray()->all() as $t) {
                FoquzContactTag::deleteAll(['answer_id' => $this->id, 'tag_id' => $t['id']]);
            }
        });
    }
}

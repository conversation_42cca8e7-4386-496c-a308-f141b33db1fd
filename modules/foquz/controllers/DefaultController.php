<?php

namespace app\modules\foquz\controllers;

use app\helpers\DateTimeHelper;
use app\helpers\DevHelper;
use app\models\Client;
use app\models\company\Company;
use app\models\Filial;
use app\models\User;
use app\models\vo\Phone;
use app\modules\foquz\models\CompanyFeedbackTheme;
use app\modules\foquz\models\ContactAdditionalFieldValue;
use app\modules\foquz\models\FilialPollKey;
use app\modules\foquz\models\FoquzComplaint;
use app\modules\foquz\models\FoquzContact;
use app\modules\foquz\models\FoquzPoll;
use app\modules\foquz\models\FoquzPollAnswer;
use app\modules\foquz\models\FoquzPollAnswerHiddenQuestion;
use app\modules\foquz\models\FoquzPollAnswerItem;
use app\modules\foquz\models\FoquzPollDishScore;
use app\modules\foquz\models\FoquzPollKey;
use app\modules\foquz\models\FoquzPollMailingListSend;
use app\modules\foquz\models\FoquzQuestion;
use app\modules\foquz\models\FoquzQuestionDetail;
use app\modules\foquz\models\FoquzQuestionFile;
use app\modules\foquz\models\FoquzQuestionFormField;
use app\modules\foquz\models\FoquzQuestionImage;
use app\modules\foquz\models\FoquzQuestionSmile;
use app\modules\foquz\models\mailings\MailingListContact;
use app\modules\foquz\models\mailings\MailingListSend;
use app\modules\foquz\models\quotes\FoquzPollLinkQuotes;
use app\modules\foquz\models\UploadForm;
use app\modules\foquz\models\VideoUploadForm;
use app\modules\foquz\models\YouTubeVideoForm;
use app\modules\foquz\services\custom\CustomQuotaService;
use app\modules\foquz\services\quotes\QuoteService;
use app\response\Response;
use FFMpeg\Coordinate\TimeCode;
use FFMpeg\FFMpeg;
use JsonException;
use Yii;
use yii\filters\AccessControl;
use yii\filters\ContentNegotiator;
use yii\filters\Cors;
use yii\filters\VerbFilter;
use yii\helpers\ArrayHelper;
use yii\helpers\Url;
use yii\web\BadRequestHttpException;
use yii\web\Controller;
use yii\web\Cookie;
use yii\web\ForbiddenHttpException;
use yii\web\NotFoundHttpException;
use yii\web\UploadedFile;

/**
 * Default controller for the `foquz` module
 */
class DefaultController extends Controller
{
    /** @inheritdoc */
    public function behaviors(): array
    {
        return [
            'access' => [
                'class' => AccessControl::className(),
                'only' => ['index', 'image-upload', 'video-upload', 'image-delete', 'upload-youtube', 'open-mail', 'open-mailing-mail', 'feedback-form'],
                'rules' => [
                    [
                        'actions' => ['open-mail', 'open-mailing-mail', 'feedback-form'],
                        'allow' => true,
                        'roles' => ['?']
                    ],
                    [
                        'allow' => true,
                        'roles' => ['@'],
                    ],
                ],
            ],
            [
                'class' => ContentNegotiator::class,
                'only' => [
                    'video-upload',
                    'image-upload',
                    'simple-upload',
                    'image-delete',
                    'answer',
                    'validate',
                    'upload-youtube',
                ],
                'formats' => [
                    'application/json' => Response::FORMAT_META_JSON,
                ],
            ],
            'corsFilter' =>  [
                'class' => Cors::class,
                'cors' => [
                    'Origin' => ['*'],
                    'Access-Control-Allow-Origin' => ['*'],
                    'Access-Control-Request-Method' => ['GET', 'POST', 'PUT', 'PATCH', 'DELETE', 'HEAD', 'OPTIONS'],
                    'Access-Control-Request-Headers' => ['*'],
                ]
            ],
            'verbs' => [
                'class' => VerbFilter::class,
                'actions' => [
                    'save-answer' => ['POST'],
                ],
            ],

        ];


    }


    public function beforeAction($action)
    {
        if($action->id === 'save-answer' || $action->id === "widget-poll") {
            $this->enableCsrfValidation = false;
        }
        if ($action->id === 'save-answer') {
            Yii::$app->user->enableSession = true;
        }
        return parent::beforeAction($action); // TODO: Change the autogenerated stub
    }

    public function actionImageUpload($id = null)
    {
        if($id) {
            $questionModel = FoquzQuestion::findOne($id);
            $basePath = "uploads/foquz/{$id}";
        } else {
            $basePath = "uploads/foquz/contact-points";
        }

        $model = new UploadForm();

        $fullPath = Yii::getAlias("@app/web/{$basePath}");

        if (false === file_exists($fullPath)) {
            mkdir($fullPath, 0777, true);
        }

        if (Yii::$app->request->getIsPost()) {
            $model->file = UploadedFile::getInstance($model, 'file');
            if ($model->file && $model->validate()) {
                $fileName = time() . '.' . $model->file->extension;
                if ($model->file->saveAs($fullPath . '/' . $fileName)) {
                    $modelFoquz = new FoquzQuestionFile();
                    $modelFoquz->file_full_path = $fullPath . '/' . $fileName;
                    $modelFoquz->file_path = $basePath . '/' . $fileName;
                    $modelFoquz->question_id = $id;
                    if($id) {
                        $modelFoquz->file_text = ArrayHelper::getValue(FoquzQuestionImage::$abc, FoquzQuestion::findOne($id)->getQuestionImages()->count() + 1, 'Z');
                        $questionModel->type = FoquzQuestion::TYPE_IMAGE;
                        $questionModel->save();
                    } else {
                        $modelFoquz->file_text = Yii::$app->request->post('label');
                    }
                    $modelFoquz->save();
                    return [
                        'id' => $modelFoquz->id,
                        'image' => $basePath . '/' . $fileName,
                        'file_text' => $modelFoquz->file_text,
                    ];
                }
            }
        }

        return ['errors' => ['message' => 'wrong params']];
    }

    public function actionChangeLabel($id)
    {
        $model = FoquzQuestionFile::findOne($id);
        $model->file_text = Yii::$app->request->post('label');
        $model->save();
    }

    public function actionSimpleUpload($id)
    {
        $model = new UploadForm();

        $basePath = "uploads/foquz/{$id}";
        $fullPath = Yii::getAlias("@app/web/{$basePath}");

        if (false === file_exists($fullPath)) {
            mkdir($fullPath, 0777, true);
        }

        if (Yii::$app->request->getIsPost()) {
            $model->file = UploadedFile::getInstance($model, 'file');
            if ($model->file && $model->validate()) {
                $fileName = time() . '.' . $model->file->extension;
                if ($res = $model->file->saveAs($fullPath . '/' . $fileName)) {
                    return [
                        'image' => '/' . $basePath . '/' . $fileName
                    ];
                }
            }

        }

        return ['errors' => ['message' => 'wrong params']];
    }

    public function actionVideoUpload($id = null)
    {
        $model = new VideoUploadForm();

        if($id) {
            $basePath = "uploads/foquz/{$id}";
        } else {
            $basePath = "uploads/foquz/contact-points";
        }

        $fullPath = Yii::getAlias("@app/web/{$basePath}");

        if (false === file_exists($fullPath)) {
            mkdir($fullPath, 0777, true);
        }

        if (Yii::$app->request->getIsPost()) {
            $model->file = UploadedFile::getInstance($model, 'file');
            if ($model->file && $model->validate()) {
                $fileName = time() . '.' . $model->file->extension;
                if ($model->file->saveAs($fullPath . '/' . $fileName)) {
                    $modelFoquz = new FoquzQuestionFile();
                    $modelFoquz->file_full_path = $fullPath . '/' . $fileName;
                    $modelFoquz->file_path = $basePath . '/' . $fileName;
                    $modelFoquz->question_id = $id;
                    $modelFoquz->type = 'video';
                    if($id) {
                        $modelFoquz->file_text = ArrayHelper::getValue(FoquzQuestionFile::$abc, FoquzQuestion::findOne($id)->getQuestionVideos()->count() + 1, 'Z');
                    } else {
                        $modelFoquz->file_text = Yii::$app->request->post('label');
                    }

                    $ffmpeg = FFMpeg::create([
                        'ffmpeg.binaries'  => Yii::$app->params['ffmpeg_binaries'],
                        'ffprobe.binaries' => Yii::$app->params['ffprobe_binaries']
                    ]);
                    $video = $ffmpeg->open($fullPath . '/' . $fileName);
                    $video
                        ->frame(TimeCode::fromSeconds(5))
                        ->save($fullPath . '/' . $fileName . '.jpg');

                    $modelFoquz->save();
                    return [
                        'model' => [
                            'id' => $modelFoquz->id,
                            'file_text' => $modelFoquz->file_text,
                            'image' => '/'.$basePath . '/' . $fileName . '.jpg',
                            'link' => '/'.$modelFoquz->file_path
                        ]
                    ];
                }
            } else {
                return ['errors' => $model->getErrors()];
            }
        }

        return ['errors' => ['message' => 'wrong params']];
    }

    public function actionUploadYoutube($id)
    {
        $model = new YouTubeVideoForm(['question' => FoquzQuestion::findOne($id), 'label' => Yii::$app->request->post('label')]);
        $questionModel = FoquzQuestion::findOne($id);

        if ($model->load(Yii::$app->getRequest()->post()) && $model->validate()) {
            if($questionModel) {
                $questionModel->type = FoquzQuestion::TYPE_VIDEO;
                $questionModel->save();
            }
            return ['model' => $model->handle()];
        }

        return ['errors' => ['message' => 'wrong params']];
    }

    public function actionUploadByLink($id = null)
    {
        $link = \Yii::$app->request->post('link');

        if(!$link) {
            return [
                'errors' => [
                'link' => 'Необходимо передавать параметр'
                ]
            ];
        }
        $questionModel = FoquzQuestion::findOne($id);

        if(stristr($link, 'https://www.youtube.com/watch?v=') || stristr($link, 'https://youtu.be/')) {
            $type = 'video';
            $attachment_content = str_replace(['https://www.youtube.com/watch?v=', 'https://youtu.be/'], '', $link);
        } else {
            $type = 'image';
            $attachment_content = $link;
        }

        $model = new FoquzQuestionFile([
            'question_id' => $id,
            'file_path' => '/',
            'file_full_path' => '/',
            'attachment_type' => 'link',
            'attachment_content' => $attachment_content,
            'type' => $type,
            'file_text' => $questionModel ? ArrayHelper::getValue(FoquzQuestionFile::$abc, FoquzQuestion::findOne($questionModel->id)->getQuestionVideos()->count() + 1, 'Z') : Yii::$app->request->post('label')
        ]);
        if($model->save()) {
            return [
                'media' => [
                    'id' => $model->id,
                    'file_text' => $model->file_text,
                    'type' => $model->type,
                    'attachment_type' => $model->attachment_type,
                    'image' => $model->getImage(),
                    'link' => $model->attachment_content
                ]
            ];
        } else {
            return ['errors' => $model->errors];
        }
    }

    public function actionImageDelete($id)
    {
        $model = FoquzQuestionFile::findOne($id);
        $model->delete();
        return [
            'questionId' => $model->question_id,
        ];
    }

    public function actionWidgetPoll2($location=null)
    {
        exit;
        $link = null;
        $liga = false;
        $data = @$_POST;
        $pollID = null;
        $user = null;
        if (!is_array($data)) $data = [];
        if ($location) {
            if (preg_match_all("@/bets/live/([^/]+)/([^/]+)-id-([\d]+)/([^/]+)-id-([\d]+)@i", $location, $arr)) {
                $data["sport"] = $arr[1][0];
                $data["category"] = $arr[2][0];
                $data["categoryId"] = $arr[3][0];
                $data["tournament"] = $arr[4][0];
                $data["tournamentId"] = $arr[5][0];
               // $liga = true;
            } else if (preg_match_all("@/PersonalFolder/Accounts/(Deposit|withdrawal)/([\d]+)/(Success|Fail)@i", $location, $arr)) {
                $data["type"] = $arr[1][0];
                $pollID = strtolower($data["type"]) == "withdrawal" ? 70918 : 70917;
                $data["transactionId"] = $arr[2][0];
                $data["status"] = $arr[3][0];
                $data['client_id'] = 55555;
                if (isset($data['client_id']) && $data["client_id"]) {
                    $user = trim($data['client_id']);
                }
                $liga = true;
            } else if (preg_match("@(furrycat|doxswf|event-types|foquz\.ru.*widget)@", $location)) {
                $link = "https://ls.foquz.ru/p/F62823b093251f?simple=1";
            }
        }

        if ($liga) {
            $poll = FoquzPoll::findOne($pollID);
            if ($user) {
                $customData = [];
                $foquzContact = FoquzContact::createContactByCompanyID($user, [], $poll->company_id, $customData);
            } else {
                $foquzContact = FoquzContact::find()->where(['phone' => '777777','company_id' => 1055])->one();
                if(!$foquzContact) {
                    $foquzContact = new FoquzContact(['company_id' => 1055,'phone' => '777777']);
                    $foquzContact->save();
                }
            }

            $answer = FoquzPollAnswer::find()->where(['foquz_poll_id'=>$pollID, 'contact_id'=>$foquzContact->id])->one();
            if (!$answer) {
                $channel = $poll->getChannels()->one();
                $answer = $channel->apiSend($foquzContact, $data, true);
                $link = 'https://ls.foquz.ru/p/'.$answer['key'].'?simple=1';
            }
        }
        Yii::$app->response->format = \yii\web\Response::FORMAT_JSON;
        return json_encode([
            "link" => $link,
            "params" => [
                "location" => "left",
                "show_immediately" => true,
            ]]);

    }

    public function actionWidgetPoll($location=null)
    {

        $location = @$_POST['location'];
        if (!$location) $location=@$_GET['location'];
        $liga = false;
        $data = @$_POST;
        $link = null;
        $pollID = null;
        $user = null;

        if (!is_array($data)) $data = [];
        if (isset($data['params']) && is_array($data['params'])) {
            foreach ($data['params'] as $k=>$v)
                if (!isset($data[$k])) $data[$k] = $v;
            unset($data['params']);
        }

        if ($location) {
            if (preg_match_all("@/PersonalFolder/Accounts/(Deposit|withdrawal)/([\d]+)/(Success|Fail)@i", $location, $arr)) {
                $data["type"] = $arr[1][0];
                $data["transactionId"] = $arr[2][0];
                $data["status"] = $arr[3][0];
                $pollID = strtolower($data["type"]) == "withdrawal" ? 70918 : 70917;
                if (isset($data['client_id']) && $data["client_id"]) {
                    $user = trim($data['client_id']);
                }
               $liga = true;
            } else if (preg_match_all("@^http(s|)://[^/]*ligastavok@i", $location, $arr) &&  isset($data['poll_id']) && isset($data['client_id']) && $data["client_id"] && $data["poll_id"]) {
                $user = trim(@$data['client_id']);
                $pollID =  trim(@$data['poll_id']);
                if ($user && $pollID) {
                    $liga = true;
                }
            } else if (preg_match_all("@^http(s|)://(.*)hrmdemo2.doxsw.com/@i", $location, $arr) || preg_match_all("@ls.html@i", $location, $arr)) {
                $link="https://demo.foquz.ru/p/F661cd5cd061c2?simple=1";
            } else if ((preg_match_all("@^http(s|)://(.*)stanki.ru@i", $location, $arr) || preg_match_all("@^http(s|)://(.*)hrmdemo2.doxsw.com/@i", $location, $arr))  && isset($data['poll_id']) && @$data["phone"] && @$data["order_guid"]) {
                $data["phone"] = preg_replace("@[^\d]+@", "", $data["phone"]);
                $companyId = 2991;
                $user = trim($data['phone']); unset($data['phone']);
                $pollID =  trim($data['poll_id']); unset($data['poll_id']);
                $noSimple = @$data['notSimple'];
                if ($user && $pollID) {
                    $poll = FoquzPoll::findOne($pollID);
                    if ($poll && $poll->company_id==$companyId) {
                        $foquzContact = FoquzContact::find()->where([
                            'phone' => $user,
                            'company_id' => $companyId,
                            'is_deleted' => false,
                        ])->one();
                        if(!$foquzContact) {
                            $foquzContact = new FoquzContact([
                                'company_id' => $companyId,
                                'phone' => $user,
                                'created_by' => 3522,
                                'updated_by' => 3522,
                            ]);
                            $foquzContact->save();
                        }
                        if ($foquzContact && $foquzContact->id) {
                            unset($data["location"]);
                            $answer = FoquzPollAnswer::find()->where(['foquz_poll_id'=>$poll->id, 'contact_id'=>$foquzContact->id])->andWhere(["like", "custom_fields", $data["order_guid"]])->one();
                            if (!$answer) {
                                $channel = $poll->getChannels()->one();
                                $answer = $channel->apiSend($foquzContact, $data, true);
                                $link = 'https://kami.foquz.ru/p/'.$answer['key'].'?simple=1';
                            }  else /*if ($answer->status!=="done") */{
                                $l = FoquzPollMailingListSend::find()->where(["answer_id"=>$answer->id])->one();
                                if ($l) $link = 'https://kami.foquz.ru/p/'.$l->key.'?simple=1';
                            }
                        }


                    }
                }

            } else if (preg_match_all("@^http(s|)://(.*)/foquz$@i", $location, $arr) && isset($data['client_id']) && $data["client_id"]) {
                $user = User::findOne(trim($data['client_id']));
                if ($user && $user->company && trim(strtolower($user->company->alias))==trim(strtolower($arr[2][0])) && $user->email && $user->created_at+60*60*24*5<time()) {
                    $count = count(FoquzPollAnswer::find()
                        ->leftJoin('foquz_poll', 'foquz_poll.id=foquz_poll_answer.foquz_poll_id')
                        ->where(['foquz_poll.company_id'=>$user->company->id, 'foquz_poll_answer.status'=>['done', 'in-progress']])
                        ->limit(50)->all());
                    if ($count==50) {
                        $companyId =  \Yii::$app->params['template_company_id'];
                        $contactData = [
                            'Email' => $user->email,
                           // 'Телефон' => $user->phone,
                            'Фамилия' => $user->name,
                            'Название компании' => $user->company->name,
                            'Домен компании' => $user->company->alias,
                            'Дата регистрации' => date('Y-m-d H:i:s', $user->created_at),
                            'Логин' => $user->username
                        ];
                        $email = isset($contactData['Email']) ? $contactData['Email'] : null;

                        if($email) {
                            $foquzContact = FoquzContact::find()->where([
                                'email' => $email,
                                'company_id' => $companyId,
                                'is_deleted' => false,
                            ])->one();
                        }

                        if(!$foquzContact) {
                            $foquzContact = new FoquzContact([
                                'company_id' => $companyId,
                                'email' => $email,
                               // 'phone' => $phone ? (string)(new Phone($phone)) : null,
                                'created_by' => 46,
                                'updated_by' => 46,
                            ]);
                            $foquzContact->save();
                        }
                        $poll = FoquzPoll::findOne(202544);
                        $customData = $foquzContact->fillData($contactData, $companyId);
                        $answer = FoquzPollAnswer::find()->where(['foquz_poll_id'=>$poll->id, 'contact_id'=>$foquzContact->id])->one();
                        if (!$answer) {
                            $channel = $poll->getChannels()->one();
                            $answer = $channel->apiSend($foquzContact, $data, true);
                            $link = 'https://corp.foquz.ru/p/'.$answer['key'].'?simple=1';
                        }

                    }
                }

            } else if (
                (preg_match_all("@^http(s|)://(.*)/cloud.html$@i", $location, $arr)  || preg_match_all("@^http(s|)://cabinet.scloud.ru@i", $location, $arr) || $location=="https://furrycat.ru/feedback2.php")
                && isset($data['poll_id']) && isset($data['client_id']) && $data["client_id"] && $data["poll_id"]) {
                $user = trim($data['client_id']); unset($data['client_id']);
                $pollID =  trim($data['poll_id']); unset($data['poll_id']);
                $noSimple = @$data['notSimple'];

                if ($user && $pollID) {
                    $companyId = 1625;
                    $poll = FoquzPoll::findOne($pollID);
                    if ($poll && $poll->company_id==$companyId) {
                        $customData = [];
                        $foquzContact = FoquzContact::createContactByCompanyID($user, $data, $poll->company_id, $customData);
                        if ($foquzContact) {
                            $answer = FoquzPollAnswer::find()->where(['foquz_poll_id'=>$poll->id, 'contact_id'=>$foquzContact->id])->one();
                            if (!$answer) {
                                $channel = $poll->getChannels()->one();
                                $answer = $channel->apiSend($foquzContact, $data, true);
                                $link = 'https://scloud.foquz.ru/p/'.$answer['key'];
                                if (!$noSimple) $link = $link.'?simple=1';
                            }
                        }

                    }
                }
            } else if (preg_match_all("@^https://foquz.ru/widgets/poll@i", $location, $arr)) {
                $link = 'https://corp.foquz.ru/p/F63087e9d2681b?simple=1';
            } else if (
                (preg_match_all("@^http(s|)://apps.skillfactory.ru@i", $location, $arr) || preg_match_all("@^http(s|)://apps.stagaqnga.skillfactory.ru@i", $location, $arr) || preg_match_all("@^http(s|)://apps.devutxmut.skillfactory.ru@i", $location, $arr)
                    || preg_match_all("@^http(s|)://apps.contented.ru@i", $location, $arr) || preg_match_all("@^http(s|)://apps.contented.stagaqnga.skillfactory.ru@i", $location, $arr) || preg_match_all("@^http(s|)://apps.contented.devutxmut.skillfactory.ru@i", $location, $arr)
                    || preg_match_all("@^http(s|)://hrmdemo3.doxsw.com@i", $location, $arr))
                && isset($data['poll_id']) && isset($data['client_id']) && $data["client_id"] && $data["poll_id"]) {

                $user = trim($data['client_id']); unset($data['client_id']);
                $pollID =  trim($data['poll_id']); unset($data['poll_id']);
                $noSimple = @$data['notSimple'];
                if ($user && $pollID) {
                    $companyId = 2760;
                    $poll = FoquzPoll::findOne($pollID);
                    if ($poll && $poll->company_id==$companyId) {
                        $customData = [];
                        $foquzContact = FoquzContact::createContactByCompanyID($user, $data, $poll->company_id, $customData);
                        if ($foquzContact) {
                            //$answer = FoquzPollAnswer::find()->where(['foquz_poll_id'=>$poll->id, 'contact_id'=>$foquzContact->id])->one();
                            //if (!$answer) {
                                $channel = $poll->getChannels()->one();
                                $answer = $channel->apiSend($foquzContact, $data, true);
                                $link = 'https://skillfactory.foquz.ru/p/'.$answer['key'];
                                //if (!$noSimple) $link = $link.'?simple=1';
                            //}
                        }

                    }
                }
            } else if (isset($data["poll_id"]) && in_array($data["poll_id"], [256140])) {
                $user = trim($data['client_id']); unset($data['client_id']);
                $pollID =  trim($data['poll_id']); unset($data['poll_id']);
                $noSimple = @$data['notSimple'];
                if ($user && $pollID) {
                    $companyId = 2647;
                    $poll = FoquzPoll::findOne($pollID);
                    if ($poll && $poll->company_id==$companyId) {
                        $customData = [];
                        $foquzContact = FoquzContact::createContactByCompanyID($user, $data, $poll->company_id, $customData);
                        if ($foquzContact) {
                            //$answer = FoquzPollAnswer::find()->where(['foquz_poll_id'=>$poll->id, 'contact_id'=>$foquzContact->id])->one();
                            //if (!$answer) {
                            $channel = $poll->getChannels()->one();
                            $answer = $channel->apiSend($foquzContact, $data, true);
                            $link = 'https://systeme.foquz.ru/p/'.$answer['key'];
                            if (!$noSimple) $link = $link.'?simple=1';
                            //}
                        }

                    }
                }
            }

        }

        if ($liga) {
            $poll = FoquzPoll::findOne($pollID);
            if ($poll->company_id==1055) {
                if ($user) {
                    $customData = [];
                    $foquzContact = FoquzContact::createContactByCompanyID($user, [], $poll->company_id, $customData);
                } else {
                    $foquzContact = FoquzContact::find()->where(['phone' => '777777','company_id' => 1055])->one();
                    if(!$foquzContact) {
                        $foquzContact = new FoquzContact(['company_id' => 1055,'phone' => '777777']);
                        $foquzContact->save();
                    }
                }
                $answer = FoquzPollAnswer::find()->where(['foquz_poll_id'=>$pollID, 'contact_id'=>$foquzContact->id])->one();
                if (!$answer) {
                    $channel = $poll->getChannels()->one();
                    $answer = $channel->apiSend($foquzContact, $data, true);
                    $link = 'https://ls.foquz.ru/p/'.$answer['key'].'?simple=1';
                }
            }
        }

        Yii::$app->response->format = \yii\web\Response::FORMAT_JSON;

        return json_encode([
            "link" => $link,
            "params" => [
                "location" => "left",
                "show_immediately" => true,
            ]]);

    }


    public function actionPoll($authKey, $questionId=null, $pollId = null, $tablet=false, $simple=false)
    {
        //$this->layout = '@app/modules/foquz/views/layouts/answer';
        $this->layout = $tablet  ? false : '@app/modules/foquz/views/layouts/answer';

        if ($authKey !== 'dummy' && $authKey !== 'dummyDesign') {
            throw new NotFoundHttpException('Page not found');
        }

        if (empty($questionId) && empty($pollId)) {
            throw new BadRequestHttpException();
        }

        $model = new FoquzPollAnswer();
        if ($questionId) {
            $currentQuestion = FoquzQuestion::findOne($questionId);
        }

        if (empty($currentQuestion) && !$pollId) {
            throw new NotFoundHttpException('Question not found');
        }

        $poll = $pollId ? FoquzPoll::findOne($pollId) : $currentQuestion->poll;

        $showFoquzLink = true;
        $tariffWarning = false;
        $tariffName = 'Базовый';
        if ($poll && $company = $poll->company) {
            $tariffName = $company->tariff->title ?? 'Базовый';
            $tariffWarning = $company->isAnswersLimitsOver();
            $showFoquzLink = !($tariffName !== 'Базовый' && !$poll->show_foquz_link);
        }

        return $this->render($tablet ? 'poll_tablet' : ($simple ? 'poll_simple' : 'poll_new'), [
            'complaint' => $complaint ?? null,
            'designModel' => $poll->design,
            'model' => $model,
            'poll' => $poll,
            'poll_id' => $poll->id,
            'pollLangs' => $poll->foquzPollLangs,
            'currentQuestion' => $currentQuestion ?? null,
            'answerForm' => $answerForm ?? null,
            'notPreview' => true,
            'authKey' => $authKey,
            'left_seconds' => self::leftSecondsCalculate($model->first_question_showed_at, $poll->time_to_pass),
            'tariffWarning' => $tariffWarning,
            'showFoquzLink' => $showFoquzLink,
            'tariffName' => $tariffName,
            'allowEditAfterDone' => false,
            'staffEdit' => false,
            'needAuth' => false,
            'accessDenied' => false,
        ]);
    }

    private static function leftSecondsCalculate($firstQuestionShowedAt, $timeToPass)
    {
        $timeToPass = DateTimeHelper::timeStringToSeconds($timeToPass);
        return ($firstQuestionShowedAt and $timeToPass) ?
            max($timeToPass - ((new \DateTime('NOW'))->getTimestamp() - (new \DateTime($firstQuestionShowedAt))->getTimestamp()), 0) :
            $timeToPass;
    }

   private static function _createContactFromParamsAscona($poll, &$customData=[])
    {
        $user = trim(Yii::$app->getRequest()->get("t"));
        if ($user) {
            $data = [];
            $data["Телефон"]=$user;
            $contact = FoquzContact::createContact($data, $poll->company_id, $customData);
            return $contact;
        }
    }

    private static function _createContactFromParamsEmail($poll, &$customData=[]) : ?FoquzContact
    {
        $user = trim(Yii::$app->getRequest()->get("email"));
        if ($user) {
            $data = [];
            $data["Email"]=$user;
            $contact = FoquzContact::createContact($data, $poll->company_id, $customData);
            return $contact;
        }
        return null;
    }

    private static function _createContactFromParams($poll, &$customData = [])
    {
        $user = Yii::$app->getRequest()->get("fz_user");

        if ($user) {
            $data = [];
            $user = explode("|", $user);
            $fields = ["Фамилия", "Имя", "Email", "Телефон", "CUSTOMER ID"];
            foreach ($fields as $i => $key) {
                if (count($user) > $i && trim($user[$i])) $data[$key] = trim($user[$i]);
            }



            $birthday = Yii::$app->getRequest()->get("fz_x_birth_date");
            if ($birthday && strtotime($birthday)) {
                $data["Дата рождения"] = $birthday;
            }

            $purchasedate = Yii::$app->getRequest()->get("fz_x_purchase_date");
            if ($purchasedate && strtotime($purchasedate)) {
                $data["Дата продажи"] = date("Y-m-d", strtotime($purchasedate));
            }

            $z_open = Yii::$app->getRequest()->get("z_open");
            if ($z_open) {
                $data["z_open"] = $z_open;
            }

            $z_open = Yii::$app->getRequest()->get("fz_extuid");
            if ($z_open) {
                $data["fz_extuid"] = $z_open;
            }

            $z_open = Yii::$app->getRequest()->get("fz_x_cluster_rfm");
            if ($z_open) {
                $data["fz_x_cluster_rfm"] = $z_open;
            }


            $z_open = Yii::$app->getRequest()->get("fz_x_cluster_style");
            if ($z_open) {
                $data["fz_x_cluster_style"] = $z_open;
            }


            $contact = FoquzContact::createContact($data, $poll->company_id, $customData);

            return $contact;
        }
    }

    private static function _createContactFromParamsByClientID($poll, &$customData=[])
    {
        $user = trim(Yii::$app->getRequest()->get("customer"));
        if ($user) {
            $data = [];
            $birthday = Yii::$app->getRequest()->get("fz_x_birth_date");
            if ($birthday && strtotime($birthday)) {
                $data["Дата рождения"] = $birthday;
            }

            $purchasedate = Yii::$app->getRequest()->get("fz_x_purchase_date");
            if ($purchasedate && strtotime($purchasedate)) {
                $data["Дата продажи"] = date("Y-m-d", strtotime($purchasedate));
            }

            $z_open = Yii::$app->getRequest()->get("z_open");
            if ($z_open) {
                $data["z_open"] = $z_open;
            }

            $z_open = Yii::$app->getRequest()->get("fz_extuid");
            if ($z_open) {
                $data["fz_extuid"] = $z_open;
            }

            $z_open = Yii::$app->getRequest()->get("fz_x_cluster_rfm");
            if ($z_open) {
                $data["fz_x_cluster_rfm"] = $z_open;
            }


            $z_open = Yii::$app->getRequest()->get("fz_x_cluster_style");
            if ($z_open) {
                $data["fz_x_cluster_style"] = $z_open;
            }


            $contact = FoquzContact::createContactByCompanyID($user, $data, $poll->company_id, $customData);

            return $contact;
        }
    }

    public function actionAnonymous(
        $id,
        $tablet = false,
        $simple = false,
        $customer = null,
        $t = null,
        $widgetPreview = false,
        $ui = null
    ) {
        $this->layout = $tablet ? false : '@app/modules/foquz/views/layouts/answer';
        $pollKey = null;
        $client = null;
        $clientEmail = null;

        /** @var FoquzPollMailingListSend|null $s */
        $s = FoquzPollMailingListSend::find()->where(["key" => $id])->one();
        $contact = null;

        if ($s) {
            $s->status = 2;
            $s->save();
            $model = FoquzPollAnswer::findOne(["id" => $s->answer_id]);
            if ($model) {
                $poll = FoquzPoll::findOne($model->foquz_poll_id);
                if ($model->contact_id) {
                    $contact = FoquzContact::findOne($model->contact_id);
                }
            }
            $quote = FoquzPollLinkQuotes::findOne($model->quote_id);
        } else {
            $pollKey = FoquzPollKey::findOne(['key' => $id]);
            $clientEmail = $pollKey ? $pollKey->clientEmail : false;
            $client = $clientEmail ? $clientEmail->client : false;
            $poll = $pollKey ? $pollKey->foquzPoll : false;
            if (!$poll) {
                $poll = $this->findPoll($id);
            }
            $model = $pollKey ? FoquzPollAnswer::findOne(['auth_key' => $pollKey->answer_key]) : false;
            $quote = ($poll) ? FoquzPollLinkQuotes::getQuoteByKey($id, $poll->id) : null;
        }
        $currentUrl = \yii\helpers\Url::to(\yii\helpers\Url::current(), 'https');

        if ($widgetPreview & !empty($poll)) {
            $firstQuestion = ArrayHelper::getValue($poll->foquzQuestions, '0.id');
            if (!empty($firstQuestion)) {
                return $this->redirect(Url::to([
                    '/foquz/default/poll',
                    'authKey'     => 'dummy',
                    'pollPreview' => 1,
                    'questionId'  => $firstQuestion,
                    'simple'      => $simple,
                ], 'https'));
            }
        }

        if (!$widgetPreview && (empty($poll) || stripos(strtolower($currentUrl),
                    strtolower($poll->company->alias)) === false)) {
            throw new NotFoundHttpException('Page not found');
        }

        if ($poll->deleted) {
            throw new NotFoundHttpException('Page not found');
        }


        $cookies = Yii::$app->getRequest()->getCookies();

        if (Yii::$app->request->get('widgetPreview') &&
            empty(Yii::$app->request->get('nopreview'))) {
            $model = new FoquzPollAnswer();
            $model->auth_key = 'dummyDesign';
            $model->foquz_poll_id = $poll->id;
        }

        if (!$model) {
            $loadCustomerAnswer = false;


            $fqzContact = null;
            if (($poll->dont_send_if_passed_link || $poll->stop_sending_link || $poll->stop_sending_link_time)) {
                if ($customer) {
                    $fqzContact = FoquzContact::find()->where([
                        'company_client_id' => $customer,
                        'company_id'        => $poll->company_id
                    ])->one();
                } elseif ($t)  {
                    $fqzContact = FoquzContact::find()->where([
                        'phone' => $t,
                        'company_id'        => $poll->company_id
                    ])->one();
                }
            }

            if (!empty($fqzContact)) {
                /** @var FoquzPollAnswer $answer */
                $answer = FoquzPollAnswer::find()
                    ->where(['contact_id' => $fqzContact->id, 'foquz_poll_id' => $poll->id])
                    ->orderBy(['created_at' => SORT_DESC])
                    ->one();

                if ($answer) {
                    $add_time = 0;
                    if ($poll->stop_sending_link_time) {
                        $hm = explode(':', $poll->stop_sending_link_time);
                        if (count($hm) === 2) {
                            list($h, $m) = $hm;
                            $add_time = ((int)$h * 60 + (int)$m)*60;
                        }
                    }
                    if (($poll->dont_send_if_passed_link && in_array($answer->status,
                                [FoquzPollAnswer::STATUS_IN_PROGRESS, FoquzPollAnswer::STATUS_DONE])) ||
                        ($poll->stop_sending_link === 'double') ||
                        (($poll->stop_sending_link || $poll->stop_sending_link_time) && $poll->stop_sending_link !== 'double' && time() < strtotime($answer->created_at) + 60 * 60 * 24 * $poll->stop_sending_link + $add_time)) {
                        $answs = $answer;
                        $authKey = $answer->auth_key;
                        $loadCustomerAnswer = true;
                    }
                }
            } elseif ($poll->need_auth && !Yii::$app->user->isGuest) {
                /** @var FoquzPollAnswer $answer */
                $answer = FoquzPollAnswer::find()
                    ->where(['user_id' => Yii::$app->user->id, 'foquz_poll_id' => $poll->id])
                    ->orderBy(['created_at' => SORT_DESC])
                    ->one();
                if ($answer) {
                    $answs = $answer;
                    $authKey = $answer->auth_key;
                    $loadCustomerAnswer = true;
                }
            } else {
                if (!empty($ui)) {
                    $answer = CustomQuotaService::getAnswerByContact($ui, $poll);
                    if (!empty($answer)) {
                        $answs = $answer;
                        $loadCustomerAnswer = true;
                        $authKey = $answs->auth_key;
                    } else {
                        $answs = null;
                        $authKey = null;
                    }
                }
            }

            $newKey = false;
            if (!$loadCustomerAnswer && empty($ui)) {
                $authKey = $cookies->get('anonymous_poll');
                $answs = null;
                if ($authKey !== null) {
                    $answs = FoquzPollAnswer::findOne(['auth_key' => $authKey]);
                }
                if (!$answs) {
                    $newKey = true;
                }
            }

            if (($authKey !== null && $answs === null) || $authKey === null || ($cookies->get('p_key') && $cookies->get('p_key')->value !== $id) || Yii::$app->request->get('kiosk') || $poll->kiosk_mode || $tablet) {
                if ($cookies->get('p' . $id) !== null && !Yii::$app->request->get('kiosk') && !$poll->kiosk_mode && !$tablet && !$newKey && empty($ui)) {
                    $anonymousKey = $cookies->get('p' . $id)->value;
                } else {
                    $anonymousKey = md5(uniqid('poll') . '_' . time());
                }

                if (!$poll->kiosk_mode && !Yii::$app->request->get('kiosk') && !$tablet && !($poll->need_auth && !Yii::$app->user->isGuest)) {
                    Yii::$app
                        ->getResponse()
                        ->getCookies()
                        ->add(new Cookie([
                            'name'   => 'anonymous_poll',
                            'value'  => $anonymousKey,
                            'expire' => time() + 60 * 60 * 24 * 365 * 10,
                        ]));

                    Yii::$app
                        ->getResponse()
                        ->getCookies()
                        ->add(new Cookie([
                            'name'   => 'p_key',
                            'value'  => $id,
                            'expire' => time() + 60 * 60 * 24 * 365 * 10,
                        ]));

                    Yii::$app
                        ->getResponse()
                        ->getCookies()
                        ->add(new Cookie([
                            'name'   => 'p' . $id,
                            'value'  => $anonymousKey,
                            'expire' => time() + 60 * 60 * 24 * 365 * 10,
                        ]));
                } else {
                    Yii::$app->getResponse()->getCookies()->remove('p_key');
                    Yii::$app->getResponse()->getCookies()->remove('anonymous_poll');
                    Yii::$app->getResponse()->getCookies()->remove('p' . $id);
                }

                if (!$poll->is_auto) {
                    $filialPollKey = FilialPollKey::findOne(['key' => $id]);
                }

                //данные клиента в GET запросе
                $customData = [];
                if (Yii::$app->getRequest()->get('fz_user')) {
                    $contact = $this->_createContactFromParams($poll, $customData);
                } else {
                    if (Yii::$app->getRequest()->get('customer')) {
                        $contact = $this->_createContactFromParamsByClientID($poll, $customData);
                    } else {
                        if (Yii::$app->getRequest()->get('email')) {
                            $contact = $this->_createContactFromParamsEmail($poll, $customData);
                        } else {
                            if (Yii::$app->getRequest()->get('t') /*&& $poll->company->id==1531*/) {
                                $contact = $this->_createContactFromParamsAscona($poll, $customData);
                            }
                        }
                    }
                }

                if (!$loadCustomerAnswer) {
                    $modelAnswer = new FoquzPollAnswer([
                        'foquz_poll_id'    => $poll->id,
                        'client_id'        => null,
                        'auth_key'         => $anonymousKey,
                        'answer_filial_id' => $filialPollKey ? $filialPollKey->filial_id : null,
                        'ip_address'       => $_SERVER['REMOTE_ADDR']
                    ]);
                } else {
                    $modelAnswer = $answs;
                }

                $modelAnswer->setProcessingTimeByChannelType(FoquzPollAnswer::CHANNEL_TYPE_LINK);

                if ($contact) {
                    $modelAnswer->contact_id = $contact->id;
                }

                if (in_array($poll->company_id, [3844, 3751])) { //для отдельных компания
                    $customData = Yii::$app->getRequest()->get();
                    if (isset($customData['id'])) {
                        unset($customData['id']);
                    }
                    if (isset($customData['lang'])) {
                        unset($customData['lang']);
                    }
                    if (!count($customData)) {
                        $customData = null;
                    }
                }

                if ($customData) {
                    if (isset($customData['edit'])) {
                        unset($customData['edit']);
                    }
                    $cf = $customData;
                    if (is_array($cf) && isset($cf["z_open"]) && $cf["z_open"]) {
                        if (preg_match_all("@\:(\w+)$@", $cf["z_open"], $codeArr)) {
                            if (count($codeArr) == 2) {
                                $codeArr = $codeArr[1][0];
                                $filial = Filial::find()->where([
                                    "company_id" => $poll->company_id,
                                    "iiko_id"    => $codeArr
                                ])->one();
                                if ($filial) {
                                    $modelAnswer->answer_filial_id = $filial->id;
                                }
                            }
                        }
                    }
                    $modelAnswer->custom_fields = json_encode($customData, JSON_UNESCAPED_UNICODE);
                }

                if (in_array($poll->id, [8091, 8077])) {
                    $modelAnswer->answer_filial_id = 186;
                }

                $modelAnswer->quote_id = $quote->id ?: null;

                if ($modelAnswer->save() && !$loadCustomerAnswer) {
                    $authKey = $anonymousKey;
                }
            }


            $model = FoquzPollAnswer::findOne(['auth_key' => $authKey]);
            if (!$model) {
                throw new NotFoundHttpException('Not found');
            }
        } else {
            $authKey = $pollKey ? $pollKey->answer_key : $model->auth_key;
        }

        if (!empty($model) && CustomQuotaService::isQuota($model->foquz_poll_id)) {
            $quota = new CustomQuotaService($model);
            if (empty($model->contact_id)) {
                $quota->setContact();
            }
            $redirectUrl = $quota->checkQuota();
            if ($redirectUrl) {
                return $this->redirect($redirectUrl);
            }
        }

        $needAuth = false;
        $accessDenied = false;
        if ($model->foquzPoll->need_auth) {
            if (Yii::$app->user->isGuest) {
                $needAuth = true;
                $accessDenied = true;
            } else {
                $user = Yii::$app->user->identity;

                if (
                    $model->foquzPoll->company_id != $user->company->id ||
                    ($model->user_id && Yii::$app->user->getId() != $model->user_id) /*||
                    (
                        !$user->isAdmin() && !$user->isExecutor() && !($model->answer_filial_id && $user->isFilialEmployee() &&
                            (empty($user->userFilials) || in_array($model->answer_filial_id, ArrayHelper::getColumn($user->userFilials, 'filial_id')))) &&
                        !(($user->isEditor() || $user->isWatcher()) && in_array($model->foquzPoll->folder_id, FoquzPoll::getEditorFolderIdsAll()['folders'])) &&
                        !($user->isRespondent() && (count($user->respondentPolls) === 0 || in_array($model->foquz_poll_id, ArrayHelper::getColumn($user->respondentPolls, 'poll_id'))))
                    )*/
                ) {
                    $accessDenied = true;
                    throw new ForbiddenHttpException("Опрос не доступен для пользователя");
                }
            }
        }

        $showFoquzLink = true;
        $tariffWarning = false;
        if ($company = $model->foquzPoll->company) {
            $tariffName = $company->tariff->title ?? 'Базовый';
            $tariffWarning = $company->isAnswersLimitsOver();
            $showFoquzLink = !($tariffName !== 'Базовый' && !$model->foquzPoll->show_foquz_link);
        }

        if ($accessDenied) {
            if ($_SERVER['HTTP_HOST'] == "sibur.foquz.ru") {
                $session = Yii::$app->session;
                if (!$session->isActive) {
                    $session->open();
                }
                $url = "https://" . $_SERVER['HTTP_HOST'] . $_SERVER["REQUEST_URI"];
                $session->set('sso_redirect', $url);
                header('Location: https://sibur.foquz.ru/adfs/saml');
                exit;
            }
            $poll = new FoquzPoll();
            $poll->is_active = 1;
            return $this->render($tablet ? 'poll_tablet' : ($simple ? 'poll_simple' : 'poll_new'), [
                'complaint'          => null,
                'designModel'        => $model->foquzPoll->design,
                'model'              => new FoquzPollAnswer(),
                'poll'               => $poll,
                'poll_id'            => $model->foquzPoll->id,
                'pollLangs'          => $model->foquzPoll->foquzPollLangs,
                'currentQuestion'    => null,
                'answerForm'         => null,
                'notPreview'         => true,
                'authKey'            => null,
                'client'             => null,
                'contact'            => null,
                'order'              => null,
                'clientEmail'        => null,
                'left_seconds'       => 0,
                'tariffWarning'      => $tariffWarning,
                'showFoquzLink'      => $showFoquzLink,
                'tariffName'         => $tariffName ?? 'Базовый',
                'vars'               => '',
                'allowEditAfterDone' => false,
                'staffEdit'          => false,
                'needAuth'           => $needAuth,
                'accessDenied'       => true,
            ]);
        }

        $questionId = ArrayHelper::getValue($poll->foquzQuestions, '0.id');

        if ($model->status === $model::STATUS_OPEN || $model->status === $model::STATUS_NEW || $model->status === "email-open") {
            $model->setUserDevice();

            $model->status = $model::STATUS_OPEN;

            //передача филиала в параметрах ссылки
            if (!$model->answer_filial_id) {
                $filialId = Yii::$app->getRequest()->get('filial');
                $filial = null;
                if (!$filialId) {
                    $filialId = Yii::$app->getRequest()->get('f');
                }
                if ($filialId) {
                    $filial = Filial::find()->where([
                        "crm_id"     => trim($filialId),
                        "company_id" => $model->foquzPoll->company_id
                    ])->one();
                }
                if (!$filial) {
                    $filial = Filial::find()->where([
                        "name"       => trim($filialId),
                        "company_id" => $model->foquzPoll->company_id
                    ])->one();
                }
                if ($filial) {
                    $model->answer_filial_id = $filial->id;
                }
            }
            $model->save();
            if (!is_null(Yii::$app->getRequest()->get('fz_answer'))) {
                $this->_answerFirstQuestion($model);
            }
        }


        if (!$model->foquzPoll->is_auto && !$model->answerChannel) {
            $listSend = new FoquzPollMailingListSend([
                'answer_id'    => $model->id,
                'status'       => FoquzPollMailingListSend::STATUS_OPEN,
                'key'          => md5(time() . uniqid()),
                'sended'       => date('Y-m-d H:i:s'),
                'channel_name' => 'Ссылка',
            ]);
            if (!$listSend->save()) {
            }
        }


        $currentQuestion = $model
            ->foquzPoll
            ->getFoquzQuestions()
            ->with(['scaleRatingSetting'])
            ->where(['id' => $questionId])->one();


        $answerForm = FoquzPollAnswerItem::findOne([
            'foquz_poll_answer_id' => $model->id,
            'foquz_question_id'    => $questionId,
        ]);

        if (!$answerForm) {
            $answerForm = new FoquzPollAnswerItem([
                'rating'               => 0,
                'foquz_poll_answer_id' => $model->id,
                'foquz_question_id'    => $questionId,
            ]);
        }

        if ($answerForm->load(Yii::$app->getRequest()->post()) && $answerForm->save()) {
            return $this->refresh();
        }
        $order = $model->order_id ? $model->order : false;
        if ($order) {
            $client = Client::findOne($order->client_id);
        }
        $complaint = FoquzComplaint::findOne(['foquz_poll_answer_id' => $model->id]);

        $model->foquzPoll->datetime_start = DateTimeHelper::addGMT3($model->foquzPoll->datetime_start);
        $model->foquzPoll->datetime_end = DateTimeHelper::addGMT3($model->foquzPoll->datetime_end);

        $model->foquzPoll->addUtmToPages($model);


        $vars = [];
        foreach (Yii::$app->request->get() as $key => $param) {
            $vars['URL.' . $key] = $param;
        }

        if ($model->foquzPoll->company_id == 2089 && $model && $model->contact_id) {
            $vals = ContactAdditionalFieldValue::find()->where(['contact_id' => $model->contact_id])->with("additionalField")->all();
            foreach ($vals as $val) {
                $vars['CLIENT.' . $val->additionalField->text] = $val->value;
            }
        }

        /*        if ($model->answer_filial_id) {
                    $filial = Filial::findOne($model->answer_filial_id);
                    if ($filial) {
                        $vars["FILIAL.param1"] = $filial->param1 ?? "";
                        $vars["FILIAL.param2"] = $filial->param2 ?? "";
                        $vars["FILIAL.param3"] = $filial->param3 ?? "";
                        $vars["FILIAL.name"] = $filial->name ?? "";
                    }
                }*/

        foreach ($model->getVariables() as $key => $value) {
            if (!isset($variables[$key])) {
                $vars[$key] = $value;
            }
        }

        if ($model->foquzPoll->need_auth && !Yii::$app->user->isGuest /*&& !$model->contact_id*/) {
            $contact = $this->_createContactByUser(Yii::$app->user->identity, $model->foquzPoll->company_id);
            if ($contact) {
                $model->contact_id = $contact->id;
                $model->save();
            }
        }

        $poll = $model->foquzPoll;
        $quoteService = new QuoteService($quote);
        if ($poll->is_published && $quote && !$quoteService->checkLinkQuote()['result']) {
            $poll->is_active = 0;
        }

        return $this->render($tablet ? 'poll_tablet' : ($simple ? 'poll_simple' : 'poll_new'), [
            'complaint'          => $complaint,
            'designModel'        => $model->foquzPoll->design,
            'model'              => $model,
            'poll'               => $model->foquzPoll,
            'poll_id'            => $model->foquzPoll->id,
            'pollLangs'          => $model->foquzPoll->foquzPollLangs,
            'currentQuestion'    => $currentQuestion,
            'answerForm'         => $answerForm,
            'notPreview'         => true,
            'authKey'            => $authKey,
            'client'             => $client,
            'contact'            => $contact,
            'order'              => $order,
            'clientEmail'        => $clientEmail,
            'left_seconds'       => self::leftSecondsCalculate($model->first_question_showed_at,
                $model->foquzPoll->time_to_pass),
            'tariffWarning'      => $tariffWarning,
            'showFoquzLink'      => $showFoquzLink,
            'tariffName'         => $tariffName,
            'vars'               => $vars,
            'allowEditAfterDone' => $this->allowEditAfterDone($model),
            'staffEdit'          => $this->isStaffEdit($model),
            'needAuth'           => $needAuth,
            'accessDenied'       => $accessDenied,
        ]);
    }

    /**
     * @param $user
     * @param $companyId
     * @return FoquzContact|array|\yii\db\ActiveRecord|null
     *
     * создание контакта из пользователя-респондента
     */
    private function _createContactByUser($user, $companyId)
    {
        $contactData = [
            'Email' => $user->email,
            'Телефон' => $user->phone,
            'Фамилия' => $user->name ? $user->name : $user->username,
            'Имя' => '',
            'Отчество' => '',
            'Дата регистрации' => date('Y-m-d H:i:s', $user->created_at),
            'Логин' => $user->username
        ];
        $email = isset($contactData['Email']) ? $contactData['Email'] : null;
        $phone = isset($contactData['Телефон']) ? $contactData['Телефон'] : null;
        $phone = trim($phone);
        if (strlen($phone)==10) $phone = "7" . $phone;
        if($email && $phone) {
            $foquzContact = FoquzContact::find()->where([
                'email' => $email,
                'company_id' => $companyId,
                'is_deleted' => false,
            ])->orWhere([
                'phone' => (string)(new Phone($phone)),
                'company_id' => $companyId,
                'is_deleted' => false,
            ])->one();
        } elseif($email) {
            $foquzContact = FoquzContact::find()->where([
                'email' => $email,
                'company_id' => $companyId,
                'is_deleted' => false,
            ])->one();
        } else {
            $foquzContact = FoquzContact::find()->where([
                'phone' => (string)(new Phone($phone)),
                'company_id' => $companyId,
                'is_deleted' => false,
            ])->one();
        }

        if(!$foquzContact) {
            $foquzContact = new FoquzContact([
                'company_id' => $companyId,
                'email' => $email,
                'phone' => $phone ? (string)(new Phone($phone)) : null,
            ]);
            $foquzContact->save();
        }

        if ($foquzContact) {
            //print_r($foquzContact); exit;
            //print_r($contactData); exit;
            $foquzContact->fillData($contactData, $companyId);
        }

        return $foquzContact;
    }

    private function _answerFirstQuestion($model)
    {

        $answer = Yii::$app->getRequest()->get('fz_answer');
        $questions = $model
            ->foquzPoll
            ->getFoquzQuestions()
            ->where(['is_tmp' => false])
            ->andWhere(['is_deleted' => false])
            ->orderBy(['position' => SORT_ASC])->all();
        foreach ($questions as $q) {
            if ($q->main_question_type == FoquzQuestion::TYPE_INTERMEDIATE_BLOCK) {
                continue;
            }
            $question = $q;
            break;
        }

        if (!$question) {
            return false;
        }

        $model->addProcessingIfNeeded();
        $changeStatus = false;
        if (in_array($question->main_question_type, [
            FoquzQuestion::TYPE_ASSESSMENT,
            FoquzQuestion::TYPE_NPS_RATING,
            FoquzQuestion::TYPE_SMILE_RATING,
            FoquzQuestion::TYPE_STAR_RATING,
        ])) {
            $answer = intval($answer);
            if ($answer >= 0 && $answer <= 10) {
                $answerForm = FoquzPollAnswerItem::findOne([
                    'foquz_poll_answer_id' => $model->id,
                    'foquz_question_id' => $question->id,
                ]);

                if (!$answerForm) {
                    $answerForm = new FoquzPollAnswerItem([
                        'rating' => 0,
                        'foquz_poll_answer_id' => $model->id,
                        'foquz_question_id' => $question->id,
                        'question_name' => $question->service_name
                    ]);
                }

                $answerForm->self_variant = null;
                $answerForm->is_self_variant = false;
                $answerForm->answer = $question->main_question_type == FoquzQuestion::TYPE_SMILE_RATING ?
                    (FoquzQuestionSmile::findAll(['foquz_question_id' => $question->id])[$answer - 1]->id ?? null) :
                    null;
                $answerForm->rating = $answer;

                $answerForm->save();
                $changeStatus = true;
            }
        } else if ($question->main_question_type == FoquzQuestion::TYPE_VARIANTS) {
            $answer = trim($answer);
            $d = FoquzQuestionDetail::find()->where(["foquz_question_id" => $question->id, "question" => $answer])->one();
            if ($d) {
                $answer = [strval($d->id)];
                $answerForm = FoquzPollAnswerItem::findOne([
                    'foquz_poll_answer_id' => $model->id,
                    'foquz_question_id' => $question->id,
                ]);

                if (!$answerForm) {
                    $answerForm = new FoquzPollAnswerItem([
                        'rating' => null,
                        'foquz_poll_answer_id' => $model->id,
                        'foquz_question_id' => $question->id,
                        'question_name' => $question->service_name
                    ]);
                }

                $answerForm->self_variant = null;
                $answerForm->is_self_variant = false;
                $answerForm->rating = null;
                $answerForm->detail_item = json_encode($answer);

                $answerForm->save();

            }
        } else {
            $answer = trim($answer);
            if ($answer) {
                $answerForm = FoquzPollAnswerItem::findOne([
                    'foquz_poll_answer_id' => $model->id,
                    'foquz_question_id' => $question->id,
                ]);

                if (!$answerForm) {
                    $answerForm = new FoquzPollAnswerItem([
                        'rating' => null,
                        'foquz_poll_answer_id' => $model->id,
                        'foquz_question_id' => $question->id,
                        'question_name' => $question->service_name
                    ]);
                }

                $answerForm->self_variant = null;
                $answerForm->is_self_variant = false;
                $answerForm->answer = $answer;
                $answerForm->rating = null;
                $answerForm->save();
                $changeStatus = true;
            }
        }

        if ($changeStatus && $model->status === $model::STATUS_OPEN) {
            $model->status = $model::STATUS_IN_PROGRESS;
            $model->save();
            $model->sendEmailNotification();
            $model->sendPushNotification();
        }
    }

    /**
     * @OA\Post (
     *     path="/foquz/default/save-answer",
     *     tags={"Прохождение"},
     *     summary="Сохранение анкеты",
     *     @OA\Parameter(
     *         name="authKey",
     *         description="Ключ анкеты",
     *         in="query",
     *         required=true,
     *         @OA\Schema(type="string")
     *     ),
     *     @OA\Parameter(
     *         name="questionId",
     *         description="ID вопроса",
     *         in="query",
     *         required=true,
     *         @OA\Schema(type="integer")
     *     ),
     *     @OA\RequestBody(
     *           @OA\MediaType(
     *               mediaType="multipart/form-data",
     *               encoding="text/plain",
     *               @OA\Schema(
     *                   @OA\Property(
     *                        property="FoquzAnswerItem",
     *                        title="Ответ",
     *                        description="Тип вопроса: Звёздный рейтинг:
     *                        rating - оценка от 1 до 5; answer - комментарий (если есть); skipped - флаг пропуска вопрса (0 или 1). Все параметры не являются обязательными. Если вопрос пропущен, отправляется пустая строка",
     *                        type="string",
     *                        nullable=true,
     *                        example="rating=4&answer=%D0%BF%D0%BF"
     *                   ),
     *               ),
     *           ),
     *       ),
     *       @OA\Response(
     *           response=200,
     *           description="Ответ успешно сохранен",
     *           @OA\JsonContent(
     *               @OA\Property(
     *                   title="Последний вопрос?",
     *                   property="lastQuestion",
     *                   type="boolean",
     *                   example=false,
     *              ),
     *              @OA\Property(
     *                  title="Количетсво баллов за ответ",
     *                  property="points",
     *                  type="boolean",
     *                  example=false,
     *              ),
     *         ),
     *     ),
     * )
     *
     */
    public function actionSaveAnswer($authKey, $questionId = null)
    {
        Yii::$app->response->format = \yii\web\Response::FORMAT_JSON;

        if($authKey === 'dummy' || $authKey === 'dummyDesign') {
            $currentQuestion = FoquzQuestion::findOne($questionId);
            $lastQuestionId = $currentQuestion->poll->getFoquzQuestions()
                ->select('id')
                ->where(['is not', 'name', NULL])
                ->andWhere(['is_deleted' => false])
                ->andWhere(['NOT', ['main_question_type' => FoquzQuestion::TYPE_INTERMEDIATE_BLOCK]])
                ->orderBy(['position' => SORT_DESC])
                ->scalar();
            return ['lastQuestion' => (int)$lastQuestionId === (int)$questionId];
        }

        if (!$model = FoquzPollAnswer::findOne(['auth_key' => $authKey])) {
            throw new NotFoundHttpException('Not found');
        }

        $quota = null;
        if (CustomQuotaService::isQuota($model->foquz_poll_id)) {
            $quota = new CustomQuotaService($model);
            $redirectURL = $quota->checkQuota();
            if (!empty($redirectURL)) {
                return  [
                    'redirect_url' => $redirectURL
                ];
            }
        }


        $isStaffEdit = $this->isStaffEdit($model);

        if($model->foquzPoll->is_published) {
            if($model->foquzPoll->datetime_start and $model->foquzPoll->datetime_start > date("Y-m-d H:i:s") && !$isStaffEdit) {
                $this->response->statusCode = 400;
                return ['errors' => ['Опрос ещё не стартовал']];
            }
            if($model->foquzPoll->datetime_end && $model->foquzPoll->datetime_end < date("Y-m-d H:i:s") && !$isStaffEdit) {
                $this->response->statusCode = 400;
                return ['errors' => ['Опрос завершён']];
            }
        }

        if ($model->status === FoquzPollAnswer::STATUS_NEW && !empty($model->sends[0]->widget_id)) {
            $model->setUserDevice();
            $model->status = FoquzPollAnswer::STATUS_IN_PROGRESS;
            $model->save();
            $model->sendEmailNotification();
            $model->sendPushNotification();
        }


        $postData = Yii::$app->getRequest()->post('FoquzAnswerItem');
        $hiddenQuestions = Yii::$app->request->post('hiddenQuestions');

        $answersForSave = [];
        if (Yii::$app->request->contentType === 'application/json') {
            try {
                $answersForSave = json_decode(Yii::$app->request->getRawBody(), true, 512, JSON_THROW_ON_ERROR);
            } catch (JsonException $e) {
                Yii::$app->response->statusCode = 400;
                return ['errors' => ['Неверный формат запроса']];
            }
        } else {
            if(is_array($postData)) {
                foreach ($postData as $qId => $stringParams) {
                    $params = [];
                    if (is_array($stringParams)) {
                        $childParams = [];
                        foreach ($stringParams as $spId => $stringParam) {
                            parse_str($stringParam, $childParams);
                            $params[$qId][$spId] = $childParams;
                        }
                        $answersForSave[$qId] = $params;
                    } else {
                        parse_str($stringParams, $params);
                        $answersForSave[$qId] = $params;
                    }
                }
            } else {
                $params = [];
                parse_str($postData, $params);
                $answersForSave[$questionId] = $params;
            }

            $result = [];
        }

        // квота на ссылку (квоты на ответы к ней привязаны)
        $linkQuote = FoquzPollLinkQuotes::findOne($model->quote_id);
        $answerQuoteCheck = ['isQuoteLimitsOver' => false, 'endScreen' => null];

        foreach($answersForSave as $questionId => $params) {
            if ($questionId=="lang") continue;

            $lastQuestionId = $model->getLastQuestionId();
            /** @var FoquzQuestion|null $currentQuestion */
            $currentQuestion = $model
                ->foquzPoll
                ->getFoquzQuestions()
                ->where(['id' => $questionId])->one();
            if (!$currentQuestion) {
                throw new NotFoundHttpException('Question not found');
            }

            // проверка условий для квот на ответ
            if ($model->foquzPoll->is_published && $currentQuestion->main_question_type === FoquzQuestion::TYPE_VARIANTS) {
                if ($linkQuote) {
                    $quoteService = new QuoteService($linkQuote);
                    $checked = $quoteService->isAnswerQuotesLimitsOver(
                        $model->foquzPoll->id,
                        $model->id,
                        $answersForSave,
                        $currentQuestion
                    );
                    if ($checked['result']) {
                        $answerQuoteCheck = ['isQuoteLimitsOver' => true, 'endScreen' => $checked['end_screen']];
                        /*return $this->asJson([
                            'isQuoteLimitsOver' => true,
                            'endScreen' => $checked['end_screen'],
                            ]
                        );*/
                    }
                }
            }

            if (
                $model->status === FoquzPollAnswer::STATUS_DONE &&
                $currentQuestion->main_question_type !== FoquzQuestion::TYPE_INTERMEDIATE_BLOCK &&
                !$isStaffEdit &&
                !$this->allowEditAfterDone($model)
            ) {
                Yii::$app->response->statusCode = 400;
                return $this->asJson(['errors' => ['Опрос уже пройден']]);
            }

            if ($model->foquzPoll->time_to_pass && $model->first_question_showed_at && !$isStaffEdit) {
                $firstQuestionShowedAt = DateTimeHelper::fromStringDateTime($model->first_question_showed_at);
                if ($firstQuestionShowedAt and
                    $firstQuestionShowedAt->getTimestamp() + DateTimeHelper::timeStringToSeconds($model->foquzPoll->time_to_pass) < (new \DateTime("NOW"))->getTimestamp()) {
                    $this->response->statusCode = 400;
                    return ['errors' => ['Время истекло']];
                }
            }

            if (!$model->contact_id && $model->linkWithClientIfPossible($currentQuestion, $answersForSave[$questionId])) {
                foreach ($model->foquzAnswer as $answerItem) {
                    $question = $answerItem->foquzQuestion;
                    if ($question->link_with_client_field) {
                        if ($question->linked_client_field == 'phone' and $question->rewrite_linked_field) {
                            $phone = FoquzContact::preformatPhone($answerItem->answer);
                            if ($model->contact->phone != $phone) {
                                if (!FoquzContact::findOne(['phone' => $phone, 'is_deleted' => false])) {
                                    $model->contact->phone = $phone;
                                    $model->contact->save();
                                    $model->refresh();
                                }
                            }
                        } else {
                            $model->contact->fillClientField($question->linked_client_field, $answerItem->answer, $question->rewrite_linked_field);
                        }
                    } elseif (($quizzes = FoquzQuestionFormField::findAll(['question_id' => $question->id, 'link_with_client_field' => 1]))) {
                        foreach ($quizzes as $item) {
                            $parsedAnswer = json_decode($answerItem->answer, true);
                            if ($item->linked_client_field == 'phone') {
                                $phone = FoquzContact::preformatPhone($parsedAnswer[$item->id]);
                                if (!$model->contact->phone) {
                                    $model->contact->phone = $phone;
                                }
                            } elseif ($item->linked_client_field == 'email') {
                                if (!$model->contact->email) {
                                    $model->contact->email = $parsedAnswer[$item->id];
                                }
                            } else {
                                $model->contact->fillClientField($item->linked_client_field, $parsedAnswer[$item->id], $item->rewrite_linked_field);
                            }
                            if (!$model->contact->save()) {
                                Yii::error($model->errors);
                            }
                        }
                    }
                }
            }
            $isEdit = $model->status === FoquzPollAnswer::STATUS_DONE;
            [$answerItem, $result] = $model->saveQuestionAnswer($currentQuestion, $lastQuestionId, $params, (int)$isStaffEdit);
            $model->pushWebhookJob($isEdit);
            if (!empty($hiddenQuestions)) {
                FoquzPollAnswerItem::deleteAll([
                    'foquz_poll_answer_id' => $model->id,
                    'foquz_question_id' => $hiddenQuestions,
                ]);
            }
            /** @var FoquzPollAnswerItem $answerItem */
            if ($answerItem) {
                FoquzPollAnswerHiddenQuestion::deleteAll(['answer_item_id' => $answerItem->id]);
                $hiddenQuestions = FoquzQuestion::find()->select('id')->where([
                    'id' => $hiddenQuestions,
                    'poll_id' => $currentQuestion->poll_id,
                    'is_deleted' => false,
                ])->column();
                if (!empty($hiddenQuestions)) {
                    $insert = [];
                    foreach ($hiddenQuestions as $hiddenQuestionId) {
                        $insert[] = [
                            'answer_item_id' => $answerItem->id,
                            'question_id' => $hiddenQuestionId,
                        ];
                    }
                    Yii::$app->db->createCommand()->batchInsert(FoquzPollAnswerHiddenQuestion::tableName(), [
                        'answer_item_id',
                        'question_id'
                    ], $insert)->execute();
                }
            }
        }

        $setTimeAnswer = isset($params['time_start']);
        if (!empty($lastQuestionId) &&
            array_key_exists($lastQuestionId, $answersForSave) &&
            empty($model->foquzPoll->displaySetting->random_order) &&
            !$setTimeAnswer
        ) {
            $model->status = FoquzPollAnswer::STATUS_DONE;
        }
        $model->webhook_sent = false;
        $model->save();

        if (!empty($quota)) {
            $quota = new CustomQuotaService($model);
            $redirectURL = $quota->checkQuota($questionId);
            if (!empty($redirectURL)) {
                return ['redirect_url' => $redirectURL];
            }
        }
        if ($answerQuoteCheck['isQuoteLimitsOver']) {
            return $this->asJson($answerQuoteCheck);
        }
        return $result;
    }

    /**
     * @deprecated
     */
    public function actionFirstQuestionShowed($answerId)
    {
        Yii::$app->response->format = \yii\web\Response::FORMAT_JSON;

        $answer = FoquzPollAnswer::findOne(['id' => $answerId]);
        if($answer) {
            $answer->first_question_showed_at = date("Y-m-d H:i:s");
            $answer->save();
            return [];
        }
//        $this->response->statusCode=404;
        return [];
    }

    public function actionAnswer($authKey, $questionId)
    {
        $selectedQuestId = Yii::$app->getRequest()->post('selectedQuest');
//        return ['errors' => ['message' => 'selectedQuestId '.$selectedQuestId.' current: '.$questionId]];
        $model = FoquzPollAnswer::findOne(['auth_key' => $authKey]);
        if ($authKey !== 'dummy') {
            if (!$model) {
                throw new NotFoundHttpException('Not found');
            }

            $lastQuestionId = $model
                ->foquzPoll
                ->getFoquzQuestions()
                ->select('id')
                ->where(['!=', 'is_tmp', 1])
                ->andWhere(['is_deleted' => false])
                ->orderBy(['position' => SORT_DESC])
                ->scalar();

            /** @var FoquzQuestion $currentQuestion */
            $currentQuestion = $model
                ->foquzPoll
                ->getFoquzQuestions()
                ->where(['id' => $questionId])->one();

            if ($selectedQuestId) {
                $getAllQuestionIdBetween = ArrayHelper::map(FoquzQuestion::find()
                    ->select(['id'])
                    ->where(['poll_id' => $currentQuestion->poll_id])
                    ->andWhere(['between', 'position', $currentQuestion->position, $selectedQuestId])
                    ->andWhere(['not in', 'position', [$currentQuestion->position, $selectedQuestId]])
                    ->asArray()
                    ->all(), 'id', 'id');

                if (!empty($getAllQuestionIdBetween)) {
                    foreach ($getAllQuestionIdBetween as $betweenCheckId) {
                        if (null === (FoquzPollAnswerItem::findOne([
                                'foquz_question_id' => $betweenCheckId,
                                'foquz_poll_answer_id' => $model->id,
                            ]))) {
                            return ['errors' => ['message' => 'Нельзя перескочить между вопросами']];
                        }
                    }
                }
            }

        } else {
            $currentQuestion = FoquzQuestion::findOne($questionId);
            $lastQuestionId = $currentQuestion->poll->getFoquzQuestions()
                ->select('id')
                ->orderBy(['position' => SORT_DESC])
                ->andWhere(['!=', 'name', ''])
                ->scalar();
            $model = new FoquzPollAnswer();
        }

        $model->addProcessingIfNeeded();

        if (!$currentQuestion) {
            throw new NotFoundHttpException('Question not found');
        }

        $answerForm = FoquzPollAnswerItem::findOne([
            'foquz_poll_answer_id' => $model->id,
            'foquz_question_id' => $questionId,
        ]);

        if (!$answerForm) {
            $answerForm = new FoquzPollAnswerItem([
                'rating' => 0,
                'foquz_poll_answer_id' => $model->id,
                'foquz_question_id' => $questionId,
                'question_name' => $currentQuestion->service_name
            ]);
        }

        $form = Yii::$app->getRequest()->post('FoquzPollAnswerItem');

        if (isset($form['rating'])) {
            if (empty($form['rating'])) {
                if(!$currentQuestion->is_required) {
                    return [
                        'lastQuestion' => ((int)Yii::$app->getRequest()->post('check', 1) && (int)$lastQuestionId === (int)$answerForm->foquz_question_id),
                        'pollName' => $currentQuestion->poll->name,
                    ];
                }
                return ['errors' => ['message' => 'Поставьте оценку, для того чтобы перейти к следующему вопросу.']];
            }

            if (!empty($form['is_self_variant']) && empty($form['self_variant'])) {
                return ['errors' => ['message' => 'Укажите свой вариант, который помешал поставить лучшую оценку.']];
            }

            if ($form['rating'] != '5' &&
                empty($form['is_self_variant']) &&
                empty($form['detail_item']) &&
                $currentQuestion->getQuestionDetails()->count() > 0 &&
                $currentQuestion->rating_type == 1 &&
                !empty($currentQuestion->detail_question)) {
                return ['errors' => ['message' => 'Уточните, что помешало поставить лучшую оценку.']];
            }
        } elseif (!empty($form['detail_item'])) {
        } else {
            if (!isset($_POST['new_tovar_answer']) && !isset($_POST['answer_item_id'])) {
                if(!$currentQuestion->is_required) {
                    return [
                        'lastQuestion' => ((int)Yii::$app->getRequest()->post('check', 1) && (int)$lastQuestionId === (int)$answerForm->foquz_question_id),
                        'pollName' => $currentQuestion->poll->name,
                    ];
                }
                return ['errors' => ['message' => 'Поставьте оценку, для того чтобы перейти к следующему вопросу.']];
            }

        }
        if ($authKey !== 'dummy') {

            $data = Yii::$app->getRequest()->post();
            /**
             * @TODO лучше пока оставить, непонятно может ли это еще что-то сломать (Task#1120)
             * */
            if(isset($data['FoquzPollAnswerItem']['detail_item'])) {
                $data['FoquzPollAnswerItem']['detail_item'] = json_encode($data['FoquzPollAnswerItem']['detail_item']);
            }

            if ($answerForm->load($data) && $answerForm->save()) {
                if (isset($_POST['new_tovar_answer'])) {
                    $dishes = [];
                    for ($i = 1; $i <= 5; $i++) $dishes[] = $_POST['answer_item_id' . $i];
                    FoquzPollDishScore::generateOrder($answerForm->id, $dishes);
                }
                if (isset($_POST['answer_item_id'])) {
                    $s = 0;
                    for ($i = 1; $i <= 5; $i++) {
                        $dish_score = FoquzPollDishScore::findOne(['id' => $_POST['answer_item_id' . $i], 'answer_id' => $model->id]);
                        $dish_score->score = $_POST['poll_dish_score' . $i];
                        $s += $dish_score->score;
                        $dish_score->save();
                    }
                    $answerForm->question_name = 'Товар';
                    $answerForm->rating = round($s / 5);
                    $answerForm->save();
                }
                if ($model->status === $model::STATUS_OPEN) {
                    $model->status = $model::STATUS_IN_PROGRESS;
                    $model->save();
                }
                if (((int)$lastQuestionId === (int)$answerForm->foquz_question_id)) {
                    $model->status = $model::STATUS_DONE;
                    $model->save();
                }
                return [
                    'lastQuestion' => ((int)Yii::$app->getRequest()->post('check', 1) && (int)$lastQuestionId === (int)$answerForm->foquz_question_id),
                    'pollName' => $model->foquzPoll->name,
                ];
            }
        } else {
            return [
                'lastQuestion' => ((int)Yii::$app->getRequest()->post('check', 1) && (int)$lastQuestionId === (int)$answerForm->foquz_question_id),
                'pollName' => $currentQuestion->poll->name,
            ];
        }

        return [
            'errors' => $answerForm->getErrors(),
        ];
    }

    public function actionValidate($authKey, $questionId)
    {
        $model = FoquzPollAnswer::findOne(['auth_key' => $authKey]);

        if (!$model) {
            throw new NotFoundHttpException('Not found');
        }

        /** @var FoquzQuestion $currentQuestion */
        $currentQuestion = $model
            ->foquzPoll
            ->getFoquzQuestions()
            ->andWhere(['!=', 'name', ''])
            ->where(['id' => $questionId])->one();

        if (!$currentQuestion) {
            throw new NotFoundHttpException('Question not found');
        }

        $answerForm = FoquzPollAnswerItem::findOne([
            'foquz_poll_answer_id' => $model->id,
            'foquz_question_id' => $questionId,
        ]);

        if (!$answerForm) {
            $answerForm = new FoquzPollAnswerItem([
                'foquz_poll_answer_id' => $model->id,
                'rating' => 0,
                'foquz_question_id' => $questionId,
            ]);
        }
        $form = Yii::$app->getRequest()->post('FoquzPollAnswerItem');

        if (isset($form['rating'])) {
            if (empty($form['rating'])) {
                if(!$currentQuestion->is_required) {
                    return [
                        'lastQuestion' => false,
                        'pollName' => $model->foquzPoll->name,
                    ];
                }
                return ['errors' => ['message' => 'Поставьте оценку, для того чтобы перейти к следующему вопросу .']];
            }

            if (!empty($form['is_self_variant']) && empty($form['self_variant'])) {
                return ['errors' => ['message' => 'Укажите свой вариант, который помешал поставить лучшую оценку.']];
            }

            if ($form['rating'] != '5' && empty($form['is_self_variant']) && empty($form['detail_item']) && $currentQuestion->getQuestionDetails()->count() > 0) {
                return ['errors' => ['message' => 'Уточните, что помешало поставить лучшую оценку.']];
            }
        } elseif (!empty($form['detail_item'])) {

        } else {
            if(!$currentQuestion->is_required) {
                return [
                    'lastQuestion' => false,
                    'pollName' => $model->foquzPoll->name,
                ];
            }
            return ['errors' => ['message' => 'Поставьте оценку, для того чтобы перейти к следующему вопросу.']];
        }

        if ($answerForm->load(Yii::$app->getRequest()->post()) && $answerForm->validate()) {
            return [
                'lastQuestion' => false,
                'pollName' => $model->foquzPoll->name,
            ];
        }

        return [
            'errors' => $answerForm->getErrors(),
        ];
    }

    protected function findPoll($id)
    {
        $poll = FoquzPoll::find()->where(['foquz_poll.key' => $id])->one();
        if ($poll) return $poll;

        $quote = FoquzPollLinkQuotes::findOne(['key' => $id]);
        if ($quote) {
            $poll = FoquzPoll::findOne($quote->poll_id);
            if ($poll) {
                return $poll;
            }
        }

        $pk = FilialPollKey::find()->where(['filial_poll_key.key' => $id])->one();
        if ($pk) {
            $poll = FoquzPoll::findOne($pk->foquz_poll_id);
            if ($poll) return $poll;
        }

        $poll = FoquzPoll::find()
            ->leftJoin('filial_poll_key', 'filial_poll_key.foquz_poll_id = foquz_poll.id')
            ->where([
                'OR',
                ['foquz_poll.key' => $id],
                ['filial_poll_key.key' => $id]
            ])
            ->one();

        if (!$poll) {
            throw new NotFoundHttpException('Poll is not found');
        }

        return $poll;
    }

    protected function findPollByKey($key)
    {
        if (null === ($model = FoquzPollKey::findOne(['key' => $key]))) {
            return false;
        }

        return $model->foquzPoll;
    }

    public function actionError()
    {
        $exception = \Yii::$app->errorHandler->exception;
        if ($exception !== null) {
            return $this->render('error', ['exception' => $exception]);
        }
    }

    public function actionTestApi()
    {
        return $this->render('test');
    }

    public function actionClose()
    {
        Yii::$app->response->cookies->add(new Cookie([
            'name' => 'info_closed',
            'value' => 1,
            'expire' => time() + 60 * 60 * 6
        ]));
    }

    public function actionOpenMail($key)
    {
        $listSend = FoquzPollMailingListSend::find()
            ->where(['key' => $key])
            ->one();
        if($listSend && $listSend->answer && $listSend->answer->status == FoquzPollAnswer::STATUS_NEW) {
            $listSend->answer->status = FoquzPollAnswer::STATUS_EMAIL_OPEN;
            $listSend->answer->save();
        }
    }

    public function actionOpenMailingMail($key)
    {
        $listSend = MailingListSend::find()
            ->where(['key' => $key])
            ->one();
        if($listSend && $listSend->status != MailingListSend::STATUS_EMAIL_OPEN) {
            $listSend->open_date = date('Y-m-d');
            $listSend->status = MailingListSend::STATUS_EMAIL_OPEN;
            $listSend->save();
            if($listSend->mailingListContact) {
                $listSend->mailingListContact->status = MailingListContact::STATUS_EMAIL_OPEN;
                $listSend->mailingListContact->save();
            }
        }
    }

    public function actionFeedbackForm($h)
    {
        $this->layout = false;
        $company = Company::findOne(['hashid' => $h]);
        if(!$company)
            throw new NotFoundHttpException();
        $params = Yii::$app->request->get();
        $filials = Filial::find()
            ->select('id, name, address, logo')
            ->where([
                'company_id' => $company->id,
                'is_active' => 1,
            ])
            ->all();
        $themes = CompanyFeedbackTheme::find()
            ->where(['in', 'company_id', [null, $company->id]])
            ->andWhere('deleted is null')
            ->all();

        return $this->render('feedback_form', [
            'filials' => $filials,
            'themes' => $themes,
            'params' => $params,
            'company' => $company
        ]);
    }

    public function isStaffEdit($model): bool
    {
        /** @var User $user */
        if (Yii::$app->request->get('edit') === '1' && !Yii::$app->user->isGuest && $user = Yii::$app->user->identity) {
            if (
                $user->isAdmin() || $user->isEditor() || ($user->isExecutor() && $user->can_edit_answers) ||
                (
                    $user->isFilialEmployee() &&
                    (
                        empty($user->userFilials) ||
                        (
                            $model->answer_filial_id &&
                            in_array($model->answer_filial_id, ArrayHelper::getColumn($user->userFilials, 'filial_id'))
                        )
                    )
                )
            ) {
                return true;
            }
        }
        return false;
    }

    public function allowEditAfterDone(FoquzPollAnswer $model): bool
    {
        return $model->foquzPoll->editing_duration !== null &&
            ($model->foquzPoll->editing_duration === 0 ||
                (strtotime($model->created_at) + $model->foquzPoll->editing_duration * 60 * 60 * 24) > time());
    }

    /**
     * Значения переменных для анкеты (пока только для параметров филиала)
     * @param $authKey
     * @return Response
     * @throws NotFoundHttpException
     */
    public function actionAnswerVariables($authKey): \yii\web\Response
    {
        $model = FoquzPollAnswer::findOne(['auth_key' => $authKey]);
        if (!$model) {
            throw new NotFoundHttpException('Анкета не найдена');
        }

        $variables = [];
        /*$filialParams = Filial::find()
            ->select(['param1', 'param2', 'param3', "name"])
            ->where(['company_id' => $model->foquzPoll->company_id, 'id' => $model->answer_filial_id])
            ->asArray()
            ->one();

        $variables['FILIAL.param1'] = $filialParams['param1'] ?? '';
        $variables['FILIAL.param2'] = $filialParams['param2'] ?? '';
        $variables['FILIAL.param3'] = $filialParams['param3'] ?? '';
        $variables['FILIAL.name'] = $filialParams['name'] ?? '';*/

        foreach ($model->getVariables() as $key => $value) {
            if (!isset($variables[$key]))  $variables[$key] = $value;
        }
        return $this->asJson($variables);
    }
}

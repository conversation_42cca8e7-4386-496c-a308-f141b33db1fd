{"Тип вопроса": "Question type", "Звездный рейтинг": "Star rating", "Варианты ответов": "Options", "Текстовый ответ": "Text answer", "Дата/время": "Date/time", "Адрес": "Address", "Загрузка файла": "File", "Анкета": "Questionnaire", "Приоритет": "Priority", "Выбор изображения/видео": "Media selection", "Рейтинг фото/видео галереи": "Gallery rating", "Смайл-рейтинг": "Smile rating", "Рейтинг NPS": "NPS rating", "Простая матрица": "Simple matrix", "Семантический дифференциал": "Semantic differential", "Оценка": "Rating", "Промежуточный блок": "Intermediate block", "Обязательный": "Required", "Обязательное": "Required", "необязательное": "optional", "Краткое название вопроса. Используется для идентификации вопроса в разделах внутри опроса: \"Ответы\" и \"Статистика\"": "The short title of the question. It is used to identify the question in the sections inside the survey: \"Answers\" and \"Statistics\"", "Название вопроса": "Question title", "Точка контакта": "Contact point", "Служебное название": "Service title", "Можно ввести краткое название вопроса, которое будет отображаться рядом с номером вопроса, например: Блюдо": "You can enter a short question name that will be displayed next to the question number, for example: Dish", "Текст вопроса": "Question text", "Инструкция": "Instruction", "Вопрос, который задаётся респонденту": "The question that is asked to the respondent", "Цвет звезд": "The color of the stars", "Количество звезд": "The amount of the stars", "Размер": "Size", "Цвет шкалы": "Scale color", "Размер шкалы": "Scale size", "Для количества звезд более 5 доступен только средний и мелкий размер. На экранах с небольшим разрешением мелкий размер будет отображаться как средний.": "For the number of stars more than 5, only medium and small sizes are available. On screens with a small resolution, the small size will be displayed as average.", "Метки": "Labels", "{count} звезда": "{count} star", "{count} звезды": "{count} stars", "{count} звезд": "{count} stars", "Галерея фото/видео": "Gallery", "Галерея": "Gallery", "Можно загрузить изображение или видео с компьютера, а также добавить по ссылке. Видео можно добавить по ссылке только с сервиса Youtube. Для изображений поддерживаются форматы: PNG, JPG, JPEG, GIF. Ограничение на размер файла 5 мб. Для видео поддерживаются форматы: MP4, WEBM. Ограничение на размер файла 10 мб. Менять порядок элементов можно с помощью перетаскивания": "You can upload an image or video from a computer, and add the link.Video can be added by reference only from YouTube service.For images supported formats: PNG, JPG, JPEG, GIF.Restriction on the size of the file 5 MB.For video, formats are supported: MP4, Webm.Restriction on the size of a file of 10 MB.You can change the order of elements by dragging", "Необходимо добавить хотя бы одно изображение или видео": "You must add at least one image or video", "Добавьте в галерею изображения или видео с вашего устройства или по ссылке": "Add images or videos to the gallery from your device or from the link", "С компьютера": "From your computer", "По ссылке": "By the link", "Уточняющий вопрос": "Clarifying question", "Допустимое количество символов в ответе": "Permissible number of characters in response", "Можно добавить вопрос, который дополнительно будет задан респонденту для уточнения его ответа. Уточняющий вопрос должен быть всегда с вариантами ответов": "You can add a question that will be additionally asked to the respondent to clarify his answer. The clarifying question should always be with multiple answers", "Комментарий": "Comment", "Для всех оценок": "For all ratings", "Один ответ": "One answer", "Несколько ответов": "Several answers", "Добавить вариант": "Add an option", "Подсказка внутри поля": "Hint inside the field", "Кол-во символов в ответе": "Number of characters in the response", "Кол-во символов в комментарии": "Number of characters in the comments", "Если подсказка не нужна, оставьте поле пустым": "If you don't need a hint, leave the field empty", "Можно добавить подсказку внутри поля ввода, которая будет отображаться пока ничего не введено": "You can add a hint inside the input field, which will be displayed until nothing is entered", "Не отображать вопрос, если ответ уже был получен": "Do not display the question if the answer has already been received", "Если ответ от респондента на вопрос был получен, то при следующем прохождении опроса этот вопрос не будет отображаться": "If the answer to the question was received from the respondent, then this question will not be displayed the next time the survey is completed", "Сбросить полученные ответы": "Reset received responses", "Новые ответы будут собираться с": "New responses will be collected from {date}", "Эта функция меняет дату проверки, с которой искать ответ респондента на вопрос, на текущую": "This function changes the verification date from which to search for the respondent's answer to the question to the current", "Варианты ответов выпадающим списком": " Answer options by drop-down list", "Случайный порядок вариантов": "Random order of answer options", "Варианты ответов для каждого респондента при прохождении будут предложены в случайном порядке": "Answer options for each user will be offered in random order during the passage", "Max кол-во выбранных ответов": "Max number of selected responses", "Однострочное": "Single-line", "Многострочное": "Multi-line", "Маска": "Mask", "Для поля ввода можно установить маску: Телефон, Почта, Число, Сайт, ФИО. Используется для получения более точного ответа на вопрос": "You can set a mask for the input field: Phone, Email, Number, Website, Full name. Used to get a more accurate answer to the question", "Без маски": "Without a mask", "Телефон": "Phone", "Почта": "Email", "Число": "Number", "Сайт": "Site", "ФИО": "Full name", "Дата": "Date", "Период": "Period", "Дата (день и месяц)": "Date (day and month)", "Фамилия": "Surname", "Имя": "Name", "Отчество": "Patronymic", "Для поля ввода с маской ФИО можно настроить поля: Фамилия, Имя, Отчество": "For the input field with the FULL NAME mask, you can configure the fields: Last Name, First Name, Patronymic", "Связать с полем из раздела «Контакты»": "Link to the field from the «Clients» section", "Можно добавить автозаполнения контактов данными при получении ответов. Для этого нужно выбрать к какому полю из раздела \"Контакты\" добавить ответ на вопрос. Используется для быстрого получения информации по контактам": "You can add autofill contacts with data when receiving responses. To do this, select which field from the \"Clients\" section to add the answer to the question. Used to quickly get information about clients", "Можно добавить автозаполнения контактов данными при получении ответов. Для этого нужно выбрать к какому полю из раздела \"Клиенты\" добавить ответ на вопрос. Используется для быстрого получения информации по клиентам": "You can add autofill contacts with data when receiving responses. To do this, select which field from the \"Clients\" section to add the answer to the question. Used to quickly get information about clients", "Связанное поле": "Related field", "Системные": "System", "Пользовательские": "Custom", "Перезаписывать существующее значение": "Overwrite an existing value", "Можно обновлять данные клиента, даже если по заданному параметру уже была информация": "You can update the client data, even if there was already information for the specified parameter", "Только дата": "Date only", "Только время": "Only time", "Дата и время": "Date and time", "Только день и месяц": "Only a day and a month", "Регионы": "Regions ", "Можно ограничить список адресов для выбора, указав регионы, районы, города и улицы": "You can limit the list of addresses to select by specifying regions, districts, cities and streets", "Районы": "Districts", "Города/населенные пункты": "Cities/localities", "Все районы": "All districts", "Все города/населенные пункты": "All cities/localities", "Фото": "Photo", "Видео": "Video", "Фото и видео": "Photos and videos", "Максимальное кол-во файлов": "Maximum number of files", "Можно ограничить количество файлов, которое может загрузить респодент при ответе на вопрос": "You can limit the number of files that a respodent can upload when answering a question", "Поле {index}": "Field {index}", "Название поля": "Field name", "Название поля, которое будет отображаться над полем ввода": "The name of the field that will be displayed above the input field", "Добавить поле": "Add a field", "Нужно указать как минимум 2 варианта ответа. При прохождении опроса изменить порядок ответов можно с помощью перетаскивания": "You need to specify at least 2 possible answers. When passing the survey, you can change the order of responses by dragging", "Нужно добавить хотя бы 2 варианта": "We need to add at least 2 options", "Обязательное перемещение вариантов": "Mandatory movement of options", "Вид смайлов": "Type of emoticons", "Сердце": "Heart", "Лайк": "Like", "Лицо": "Face", "Количество": "Quantity", "Два": "Two", "Три": "Three", "Пять": "Five", "Дизайн": "Design", "Цветной": "Color", "Черно-белый": "Black and white", "Свой градиент": "Your graditn", "Начальный цвет": "Initial color", "Конечный цвет": "End color", "Метка начальной точки": "Start point label", "Метка конечной точки": "End point label", "Маловероятно": "Unlikely", "С большой вероятностью": "Highly likely", "Если метки не нужны, оставьте поля пустыми": "If you don't need labels, leave the fields empty", "В этом варианте вопроса можно выбрать только один ответ для каждой строки": "In this version of the question, you can choose only one answer for each row", "Форма кнопок": "Shape buttons", "Случайный порядок строк в матрице": "Random order of rows in the matrix", "Строки матрицы для каждого пользователя при прохождении будут предложены в случайном порядке": "The rows of the matrix for each user will be offered in random order when passing", "Добавить столбец": "Add a column", "Добавить строку": "Add a line", "Прямоугольник": "Rectangle", "Круг": "Circle", "Текст": "Text", "Изображение": "Image", "Можно ввести произвольное описание, которое будет отображаться внутри вопроса. Может использоваться для уточнения основного вопроса": "You can enter an arbitrary description that will be displayed inside the question. It can be used to clarify the main question", "Загрузка изображений": "Uploading images", "Можно загрузить изображения только с компьютера. Поддерживаются форматы: PNG, JPG, JPEG, GIF. Ограничение на размер файла - 5 мб": " You can download images only from your computer. Supported formats: PNG, JPG, JPEG, GIF. The file size limit is 5 MB", "Необходимо добавить хотя бы одно изображение": "It is necessary to add at least one image", "Загрузка видео": "Video download", "Можно загрузить видео с компьютера, либо с сервиса Youtube. Поддерживаются форматы: MP4, WEBM. Ограничение на размер файла 10 мб": "You can download videos from your computer or from the Youtube service. Supported formats: MP4, WEBM. The file size limit is 10 MB", "По ссылке на Youtube": "By the link on Youtube", "Необходимо добавить хотя бы одно видео": "You need to add at least one video", "Тип оценки / выбора": "Rating / selection type", "Для вопроса можно добавить рейтинг со звёздами, либо варианты на выбор": "For a question, you can add a rating with stars, or options to choose from", "Тип экрана": "Screen type", "5 звезд": "5 stars", "Варианты на выбор": "Options to choose from", "Отображать номер вопроса": " Display the question number", "Текстовый блок": "Text block", "Стартовый экран": "Start screen", "Конечный экран": "End screen", "Текст на странице": "Text on the page", "Текст кнопки «пройти опрос»": "The text of the «take a survey button»", "Пройти опрос": "Take the survey", "Кнопка «Пожаловаться»": "The «Complain» button", "Ссылка «Пожаловаться»": "The «Complain» link", "Отписаться от рассылки": "Unsubscribe from the mailing list", "Ссылка «Отписаться от рассылки»": "Link «unsubscribe from mailing»", "Функция используется для добавления жалобы": "The function is used to add a complaint", "Функция используется для возможности отписаться от рассылки опросов": "The function is used for the ability to unsubscribe from the mailing of surveys", "Текст ссылки «Пожаловаться»": "he text of the «complain» link", "Текст ссылки «Отписаться»": "The text of the «unsubscribe» link", "Пожаловаться": "<PERSON><PERSON><PERSON>", "Ссылка на внешний ресурс": "Link to external resource", "Размеры": "Sizes", "ширина x высота": "Width x height", "Если нужны исходные размеры изображения, оставьте в полях 0": "If the original image sizes are needed, leave in the fields 0", "Описание изображения": "Image description", "Ряд {number}": "Row {number}", "0 баллов": "0 points", "Удалить": "Remove", "Свой вариант": "Your option", "Свой вариант (произвольное поле)": "Your option (arbitrary field)", "Выбор одного варианта": "Select one option", "Выбор нескольких вариантов": "Selection of several options", "Можно изменить тип вопроса, выбрав пункт в раскрывающемся списке. Каждому типу вопроса соответствует свой набор настроек. При смене типа вопроса несовместимые настройки не сохраняются": "You can change the type of question by selecting the item in the drop-down list. Each type of question corresponds to its set of settings. When changing the type of question, incompatible settings are not saved.", "Добавить видео": "Add video", "Ссылка на видеоролик": "Link to video", "На данный момент вы можете добавить видео только с сервиса Youtube.": "At the moment you can add a video only from the YouTube service.", "Расположите ответы в том порядке, который будет считаться правильным ответом. При прохождении опроса они будут выводится в случайном порядке. Введите количество баллов за правильный ответ.": "Place the answers in the order that will be considered correct answer.When passing a survey, they will be excreted in random order.Enter the number of points for the correct answer.", "Баллов за правильный ответ": "Points for the correct answer", "Правильный ответ": "Correct answer", "Звёзды 5": "Stars 5", "Мин. сумма блюда для оценки": "Min.Evaluation", "Оценить все сразу": "Rate all at once", "Свой комментарий (произвольное поле)": "Your comment (arbitrary field)", "Иконки соцсетей": "Social network icons", "Отображаются только для анонимных опросов": "Displays only for anonymous polls", "Нужно переместить хотя бы {count} карточек": "Please move at least {count} cards", "Использовано максимальное количество карточек": "Maximum number of cards used", "Показать вопрос": "Show question", "Готово": "Done"}
// ko/pages/poll/stats/question-types/first-click-test/style.less

.first-click-stats {
  &__summary {
  }

  &__metric {
  }

  &__metric-label {
  }

  &__metric-value {
  }

  // Mode toggle buttons
  &__mode-toggle {
  }

  &__mode-btn {

    &--active {
    }
  }
  
  // Heatmap View specific class (wrapper for image and table)
  &__heatmap-view {
    margin: 20px 0;
  }


  &__image-wrapper {
    position: relative;
    display: inline-block; // Changed from block to allow centering if needed
    max-width: 100%;
    width: 100%; // Ensure it takes full width of its container

    &--limited {
      max-height: 400px; // As per guide
      overflow: hidden;
      border: 1px solid #eee; // Added a subtle border
    }
  }

  &__image {
    max-width: 100%;
    height: auto;
    display: block;
  }

  &__heatmap-overlay {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    pointer-events: none;
  }

  &__areas-overlay {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    pointer-events: none; // Parent is none, children can be all
  }

  &__area {
    position: absolute;
    border: 2px solid #3F51B5;
    background-color: rgba(63, 81, 181, 0.2);
    cursor: pointer;
    pointer-events: all; // Make areas clickable
    transition: all 0.2s ease;
    box-sizing: border-box; // Include border in width/height

    &:hover {
      background-color: rgba(63, 81, 181, 0.4);
      border-color: #303F9F;
      z-index: 10;
    }
  }

  &__area-label {
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    background: rgba(255, 255, 255, 0.95);
    padding: 4px 8px;
    border-radius: 4px;
    font-weight: bold;
    font-size: 12px;
    white-space: nowrap;
    box-shadow: 0 2px 4px rgba(0,0,0,0.1);
    color: #333; // Ensure text color
  }

  // Controls for heatmap
  &__controls {
    display: flex;
    // justify-content: space-between; // Will be handled by header-controls
    align-items: center;
    margin-top: 15px; // Added margin for spacing from image
    gap: 10px;
  }

  // Charts
  &__time-chart,
  &__zones-chart {
    margin: 20px 0;
  }

  &__chart-container { // Generic container for charts if needed
    min-height: 300px; // As per guide
  }

  // Filter table for heatmap areas
  &__filter-table {
  }

  &__table {
    width: 100%;
  }

  &__table-head {
  }

  &__table-header {
  }

  &__table-row {
  }

  &__table-cell {
  }

  // Checkbox styling
  &__checkbox {
  }

  &__checkbox-input {
    margin-right: 8px;
  }

  &__checkbox-label {
  }

  // Legend table (for zones distribution primarily)
  &__legend {
  }

  &__legend-table {
  }

  &__legend-head {
  }

  &__legend-header {
  }

  &__legend-row {
    // border-bottom: 1px solid #eee; // Applied to cells

    &--clickable {
      cursor: pointer;
    }
  }

  &__legend-cell {
  }

  &__legend-item {
  }

  &__legend-text {
  }

  // Color indicators
  &__color-indicator {
  }

  // Additional elements from template
  &__header-controls {
  }

  &__visualization {
  }

  &__chart-wrapper {
  }

  &__all-answers-btn {
    cursor: pointer;
  }

  // Responsive adjustments
  @media (max-width: 768px) {
    &__metric {
    }

    &__header-controls {
    }

    &__controls { // Specific controls for heatmap
      flex-direction: column;
      align-items: stretch; // Make buttons full width
    }
    
    &__mode-toggle {
      justify-content: center; // Center toggle buttons
    }

    &__area-label {
    }

    &__table-header,
    &__table-cell,
    &__legend-header,
    &__legend-cell {
    }
  }
}
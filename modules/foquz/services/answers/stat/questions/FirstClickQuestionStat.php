<?php
declare(strict_types=1);

namespace app\modules\foquz\services\answers\stat\questions;

use app\modules\foquz\models\FoquzQuestion;
use app\modules\foquz\models\FoquzQuestionFirstClick;

class FirstClickQuestionStat extends QuestionStat
{
    private array $statistics = [];
    private array $clickAreas;

    public function __construct(FoquzQuestion $question)
    {
        parent::__construct($question);
        $this->clickAreas = $question->firstClickArea;
    }
    public function getItemFields(): array
    {
        return [QuestionStat::FIELD_DETAIL_ITEM];
    }

    public function countItem($item): void
    {
        if (!empty($item[QuestionStat::FIELD_SKIPPED])) {
            $this->answersCount++;
        }

        if (!empty($item[QuestionStat::FIELD_DETAIL_ITEM])) {
            $answer = $item[QuestionStat::FIELD_DETAIL_ITEM];
            $points = FoquzQuestionFirstClick::parseAnswer($answer);
            if (count($points)) {
                $this->statistics['points'][] = $points;
            }
        }
    }

    protected function getData(): array
    {
        return [
            'statistics' => $this->statistics,
            'clickAreas' => $this->clickAreas
        ];
    }
}
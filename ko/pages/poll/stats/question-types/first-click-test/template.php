<!-- ko/pages/poll/stats/question-types/first-click-test/template.php -->

<template id="first-click-test-question-stats-template">
  <!-- ko template: { afterRender: $component.onInit.bind($component) } -->

  <div class="poll-stats poll-stats--first-click-test first-click-stats">
    <div class="question-statistics__question-variant-statistics question-statistics__variant-statistics first-click-stats__statistics">
      <div class="question-statistics__variant-statistics-chart-wrapper first-click-stats__chart-wrapper">
        <div class="d-flex justify-content-between align-items-center">
          <div class="question-statistics__variant-statistics-mode-toggle first-click-stats__mode-toggle">
            <button class="btn btn-icon btn-icon--simple question-statistics__variant-statistics-mode-button question-statistics__variant-statistics-column-mode-button first-click-stats__mode-btn"
                    title="Столбчатая диаграмма"
                    data-bind="click: function() { mode('column'); },
                               css: { 'question-statistics__variant-statistics-mode-button--active': mode() === 'column' }, tooltip">
            </button>
            <button class="btn btn-icon btn-icon--simple question-statistics__variant-statistics-mode-button question-statistics__variant-statistics-pie-mode-button first-click-stats__mode-btn"
                    title="Тепловая карта"
                    data-bind="click: function() { mode('heatmap'); },
                               css: { 'question-statistics__variant-statistics-mode-button--active': mode() === 'heatmap' }, tooltip">
            </button>
          </div>

          <!-- Heatmap specific controls -->
          <!-- ko if: mode() === 'heatmap' -->
          <div class="d-flex">
            <fc-button params="
              color: 'primary',
              mode: 'outline',
              label: 'Показать изображение',
              click: openHeatmapDetailsModal
            "></fc-button>

            <fc-button params="
              color: 'primary',
              mode: 'text',
              label: showAreas() ? 'Скрыть области' : 'Показать области',
              click: toggleAreas
            " class="ml-2"></fc-button>
          </div>
          <!-- /ko -->
        </div>

        <!-- Column Chart -->
        <div class="question-statistics__variant-statistics-chart question-statistics__variant-statistics-column-chart first-click-stats__column-chart"
             data-bind="slide: mode() === 'column', attr: { id: chartIds.column }">
        </div>

        <!-- Heatmap View -->
        <div class="first-click-stats__heatmap-view" data-bind="slide: mode() === 'heatmap'">
          <div class="first-click-stats__image-wrapper first-click-stats__image-wrapper--limited">
            <img data-bind="attr: { src: imageUrl }" class="first-click-stats__image" alt="Test image" />

            <!-- Heatmap overlay -->
            <div class="first-click-stats__heatmap-overlay" data-bind="attr: { id: 'heatmap-' + $component.id }"></div>

            <!-- Click areas overlay -->
            <!-- ko if: showAreas -->
            <div class="first-click-stats__areas-overlay">
              <!-- ko foreach: visibleAreas -->
              <div class="first-click-stats__area"
                   data-bind="
                     style: {
                       left: x + '%',
                       top: y + '%',
                       width: width + '%',
                       height: height + '%'
                     },
                     click: function() { $parent.openAreaDetails($data); },
                     attr: { title: name + ': ' + clicks + ' кликов (' + percentage + '%)' }
                   ">
                <div class="first-click-stats__area-label" data-bind="text: percentage + '%'"></div>
              </div>
              <!-- /ko -->
            </div>
            <!-- /ko -->
          </div>

          <!-- Areas filter table -->
          <div class="first-click-stats__filter-table">
            <table class="first-click-stats__table">
              <thead class="first-click-stats__table-head">
                <tr class="first-click-stats__table-row">
                  <th class="first-click-stats__table-header">
                    <label class="first-click-stats__checkbox">
                      <input type="checkbox" class="first-click-stats__checkbox-input"
                             data-bind="checked: allAreasVisible"> <!-- Removed click binding, using pureComputed -->
                      <span class="first-click-stats__checkbox-label">Область</span>
                    </label>
                  </th>
                  <th class="first-click-stats__table-header first-click-stats__table-header--right">Кол-во кликов</th>
                  <th class="first-click-stats__table-header first-click-stats__table-header--right">Процент</th>
                </tr>
              </thead>
              <tbody class="first-click-stats__table-body" data-bind="foreach: processedAreas">
                <tr class="first-click-stats__table-row">
                  <td class="first-click-stats__table-cell">
                    <label class="first-click-stats__checkbox">
                      <input type="checkbox" class="first-click-stats__checkbox-input"
                             data-bind="checked: visible, click: function() { $parent.toggleAreaVisibility($data); }">
                      <span class="first-click-stats__checkbox-label first-click-stats__checkbox-label--with-indicator">
                        <div class="first-click-stats__color-indicator"
                             data-bind="style: { backgroundColor: color }">
                        </div>
                        <span data-bind="text: name"></span>
                      </span>
                    </label>
                  </td>
                  <td class="first-click-stats__table-cell first-click-stats__table-cell--right" data-bind="text: clicks"></td>
                  <td class="first-click-stats__table-cell first-click-stats__table-cell--right" data-bind="text: percentage + '%'"></td>
                </tr>
              </tbody>
            </table>
          </div>
        </div>

        <!-- Summary Statistics - moved below chart -->
        <!-- ko if: mode() === 'column' -->
        <div class="first-click-stats__summary mt-3">
          <div class="first-click-stats__metric">
            <span class="first-click-stats__metric-label">Всего кликов: </span>
            <span class="first-click-stats__metric-value" data-bind="text: question.totalClicks"></span>
          </div>
          <div class="first-click-stats__metric">
            <span class="first-click-stats__metric-label">Среднее время выполнения, сек: </span>
            <span class="first-click-stats__metric-value" data-bind="text: question.avgExecutionTime + ' сек'"></span>
          </div>
        </div>
        <!-- /ko -->
      </div>

      <!-- Areas Table (Legend) -->
      <div class="question-statistics__variant-statistics-legend poll-stats-legend variants-statistics-legend first-click-stats__legend" data-bind="nativeScrollbar">
        <table class="table foq-table question-statistics__variant-statistics-legend-table variants-statistics-legend-table first-click-stats__legend-table mb-0">
          <thead class="position-sticky sticky-top bg-white">
            <tr>
              <th>Область клика</th>
              <th class="text-right question-statistics__variant-statistics-legend-table-vote-count-head-cell">
                Кол-во кликов
              </th>
              <th class="text-right question-statistics__variant-statistics-legend-table-percentage-head-cell">
                Процент
              </th>
            </tr>
          </thead>
          <tbody>
            <!-- ko foreach: question.processedAreas --> <!-- Use question.processedAreas for consistency -->
            <tr class="question-statistics__variant-statistics-legend-table-row first-click-stats__legend-row"
                data-bind="click: function() { $parent.openAreaDetails($data); }">
              <td class="question-statistics__variant-statistics-legend-table-text-cell first-click-stats__legend-cell">
                <div class="question-statistics__variant-statistics-legend-table-text-cell-content first-click-stats__legend-item">
                  <div class="question-statistics__variant-statistics-legend-table-color-indicator first-click-stats__color-indicator"
                       data-bind="style: { backgroundColor: color }">
                  </div>
                  <span data-bind="text: name"></span>
                </div>
              </td>
              <td align="right" class="question-statistics__variant-statistics-legend-table-vote-count-cell first-click-stats__legend-cell" data-bind="text: clicks"></td>
              <td align="right" class="question-statistics__variant-statistics-legend-table-percentage-cell first-click-stats__legend-cell" data-bind="text: percentage + '%'"></td>
            </tr>
            <!-- /ko -->

            <!-- Пользовательские точки row -->
            <!-- ko if: question.statistics.userDefinedPointsClicks > 0 -->
            <tr class="question-statistics__variant-statistics-legend-table-row first-click-stats__legend-row"
                data-bind="click: openUserPointsDetails">
              <td class="question-statistics__variant-statistics-legend-table-text-cell first-click-stats__legend-cell">
                <div class="question-statistics__variant-statistics-legend-table-text-cell-content first-click-stats__legend-item">
                  <div class="question-statistics__variant-statistics-legend-table-color-indicator first-click-stats__color-indicator first-click-stats__color-indicator--user-points"></div>
                  <span>Пользовательские точки</span>
                </div>
              </td>
              <td align="right" class="question-statistics__variant-statistics-legend-table-vote-count-cell first-click-stats__legend-cell" data-bind="text: question.statistics.userDefinedPointsClicks"></td>
              <td align="right" class="question-statistics__variant-statistics-legend-table-percentage-cell first-click-stats__legend-cell" data-bind="text: question.statistics.userDefinedPointsPercent + '%'"></td>
            </tr>
            <!-- /ko -->

            <!-- Респондент отказался от ответа row -->
            <!-- ko if: question.statistics.skippedCount > 0 -->
            <tr class="question-statistics__variant-statistics-legend-table-row first-click-stats__legend-row"
                data-bind="click: openSkippedDetails">
              <td class="question-statistics__variant-statistics-legend-table-text-cell first-click-stats__legend-cell">
                <div class="question-statistics__variant-statistics-legend-table-text-cell-content first-click-stats__legend-item">
                  <div class="question-statistics__variant-statistics-legend-table-color-indicator first-click-stats__color-indicator first-click-stats__color-indicator--skipped" data-bind="style: { backgroundColor: '#DADFE3' }"></div>
                  <span>Респондент отказался от ответа</span>
                </div>
              </td>
              <td align="right" class="question-statistics__variant-statistics-legend-table-vote-count-cell first-click-stats__legend-cell" data-bind="text: question.statistics.skippedCount"></td>
              <td align="right" class="question-statistics__variant-statistics-legend-table-percentage-cell first-click-stats__legend-cell" data-bind="text: question.statistics.skippedPercent + '%'"></td>
            </tr>
            <!-- /ko -->
          </tbody>
        </table>
      </div>
    </div>

    <!-- View all answers button -->
    <a class="no-print question-statistics__question-additional-button
      question-statistics__question-all-profiles-button first-click-stats__all-answers-btn"
       data-bind="click: onAllAnswersClick">
      Все ответы
    </a>
  </div>

  <!-- /ko -->
</template>

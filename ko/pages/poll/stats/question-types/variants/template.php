<template id="variants-question-stats-template">
  <!-- ko template: { afterRender: $component.onInit.bind($component) } -->

  <div class="poll-stats poll-stats--variants">
    <!-- ko if: $component.question.gallery.length -->
    <div class="gallery-button no-print f-color-text mt-3 mb-4 d-flex align-items-center cursor-pointer" data-bind="fancyboxGalleryItem: {
            gallery: $component.question.gallery.map(function(i) {
              return {
                src: i.url,
                opts: {
                  caption: i.description
                }
              }
            }),
            noCursor: true,
            index: 0,
          }">
      <span class="f-icon f-icon--picture f-icon-sm mr-2">
        <svg>
          <use href="#picture-icon"></use>
        </svg>
      </span>
      <span class="f-color-primary f-fs-3 bold">Галерея</span>
    </div>
    <!-- /ko -->
    <!-- ko if: question.withPoints -->
    <div class="d-flex flex-wrap  mt-10p mt-md-20p  f-fs-2 points-block">
      <div class="mr-4  mb-md-3 mb-1">
        <span class="f-color-service">Max, баллов: </span>
        <span class="bold" data-bind="text: question.maxPoints"></span>
      </div>
      <div class="mr-4  mb-md-3 mb-1">
        <span class="f-color-service">Среднее, баллов: </span>
        <span class="bold">
          <span data-bind="text: question.avgPoints"></span>
          /
          <span data-bind="text: question.avgPointsPercent + '%'"></span>
        </span>
      </div>
      <div class=" mb-md-3 mb-1">
        <span class="f-color-service">Правильный вариант: </span>
        <span data-bind="text: question.correctVariantsText"></span>
      </div>
    </div>
    <!-- /ko -->
    <div class="question-statistics__question-variant-statistics question-statistics__variant-statistics">
      <div class="question-statistics__variant-statistics-chart-wrapper">
        <div class="d-flex justify-content-between align-items-center">
          <div class="question-statistics__variant-statistics-mode-toggle">
            <button class="btn btn-icon btn-icon--simple question-statistics__variant-statistics-mode-button question-statistics__variant-statistics-column-mode-button" title="Столбчатая диаграмма" data-bind="click: function () { mode('column'); }, css: { 'question-statistics__variant-statistics-mode-button--active': mode() === 'column' }, tooltip ">
            </button>
            <!-- ko ifnot: question.variantsType > 0 -->
            <button class="btn btn-icon btn-icon--simple question-statistics__variant-statistics-mode-button question-statistics__variant-statistics-pie-mode-button" title="Круговая диаграмма" data-bind="click: function () { mode('pie'); }, css: { 'question-statistics__variant-statistics-mode-button--active': mode() === 'pie' }, tooltip ">
            </button>
            <!-- /ko -->
          </div>

          <!-- ko if: question.hasRemoved -->
          <fc-button params="color: 'primary', mode: 'text', label: showRemoved() ? 'Скрыть удаленные' : 'Показать удаленные', click: function() {
              $component.showRemoved(!$component.showRemoved());
            }"></fc-button>
            <!-- /ko -->
        </div>
        <div class="question-statistics__variant-statistics-chart question-statistics__variant-statistics-column-chart" data-bind="slide: mode() === 'column', attr: { id: chartIds.column }">
        </div>
        <div class="question-statistics__variant-statistics-chart question-statistics__variant-statistics-pie-chart" data-bind="slide: mode() === 'pie', attr: { id: chartIds.pie }">
        </div>
      </div>
      <div class="question-statistics__variant-statistics-legend poll-stats-legend variants-statistics-legend" data-bind="nativeScrollbar">
        <table class="table foq-table question-statistics__variant-statistics-legend-table variants-statistics-legend-table mb-0">
          <thead class="position-sticky sticky-top bg-white">
            <tr>
              <th>Вариант ответа</th>
              <!-- ko if: withPoints -->
              <th class="text-right question-statistics__variant-statistics-legend-table-vote-count-head-cell">
                Баллов за ответ
              </th>
              <!-- /ko -->
              <th class="text-right question-statistics__variant-statistics-legend-table-vote-count-head-cell">
                Кол-во ответов
              </th>
              <th class="text-right question-statistics__variant-statistics-legend-table-percentage-head-cell">
                Процент
              </th>
            </tr>
          </thead>
          <tbody>
            <!-- ko foreach: variants -->
            <!-- ko if: $data.is_deleted != '1' -->
            <tr class="question-statistics__variant-statistics-legend-table-row" data-bind="
                                    click: function () { $component.onVariantClick($data); },
                                    class: legendTableRowClass,
                                    css: { 'question-statistics__variant-statistics-legend-table-row--disabled': !enabled() }
                                ">
              <td class="question-statistics__variant-statistics-legend-table-text-cell">
                <div class="question-statistics__variant-statistics-legend-table-text-cell-content">
                  <div class="question-statistics__variant-statistics-legend-table-color-indicator" data-bind="style: { backgroundColor: $component.colors[$index()] }">
                  </div>
                  <!-- ko if: $data.file_id -->
                  <file-loader-preview class="file-loader-preview file-loader-preview_stats mr-10p" data-bind="click: function (_, event) {
                      event.stopPropagation();
                    }," params="loading: false, disabled: true, file: $data.file_url, preview: $data.preview_url,
                    onRemove: function() { 
                        variant.file(null)
                        variant.value('')
                    }">

                  </file-loader-preview>
                  <!-- /ko -->
                  <span>
                    <span>
                      <!-- ko text: text -->
                      <!-- /ko -->
                      <!-- ko if: $parent.question.correctVariantsIds.includes($data.id) -->
                      <svg-icon params="name: 'check'" class="svg-icon--sm f-color-text ml-1"></svg-icon>
                      <!-- /ko -->
                    </span>
                    <!-- ko if: $data.linkWithClientField -->
                    <small class="question-statistics__service-text" data-bind="text: '(связан с параметром ' + $data.linkedClientField + ' контакта)'"></small>
                    <!-- /ko -->
                  </span>
                </div>
              </td>
              <!-- ko if: $parent.withPoints -->
              <td align="right" class="question-statistics__variant-statistics-legend-table-vote-count-cell" data-bind="text: $data.points || 0">
              </td>
              <!-- /ko -->
              <td align="right" class="question-statistics__variant-statistics-legend-table-vote-count-cell" data-bind="text: $component.value[$data.customIndex]">
              </td>
              <td align="right" class="question-statistics__variant-statistics-legend-table-percentage-cell" data-bind="text: $component.getPercent($component.value[$data.customIndex])">
              </td>
            </tr>
            <!-- /ko -->
            <!-- /ko -->

            <!-- ko if: question.skip -->
            <tr class="question-statistics__variant-statistics-legend-table-row" data-bind="
                                    click: function () { $component.onVariantClick('skipped'); },
                                ">
              <td class="question-statistics__variant-statistics-legend-table-text-cell">
                <div class="question-statistics__variant-statistics-legend-table-text-cell-content">
                  <div class="question-statistics__variant-statistics-legend-table-color-indicator" data-bind="style: { backgroundColor: '#DADFE3' }">
                  </div>
                  <span>
                    Респондент отказался от ответа

                  </span>
                </div>
              </td>
              <!-- ko if: $parent.withPoints -->
              <td align="right" class="question-statistics__variant-statistics-legend-table-vote-count-cell">
              </td>
              <!-- /ko -->
              <td align="right" class="question-statistics__variant-statistics-legend-table-vote-count-cell" data-bind="text: question.skipped">
              </td>
              <td align="right" class="question-statistics__variant-statistics-legend-table-percentage-cell" data-bind="text: $component.getPercent(question.skipped)">
              </td>
            </tr>
            <!-- /ko -->

            <!-- ko if: question.hasRemoved -->
            <tr class="question-statistics__variant-statistics-legend-table-row">
              <td class="question-statistics__variant-statistics-legend-table-text-cell" data-bind="attr: {
                colspan: $parent.withPoints ? 4 : 3
              }">
                <!-- ko ifnot: question.showRemoved -->
                <fc-button params="label: 'Показать удаленные', click: function() {
                  question.showRemoved(true)
               }, mode: 'text', color: 'primary', "></fc-button>
                <!-- /ko -->
                <!-- ko if: question.showRemoved -->
                <fc-button params="label: 'Скрыть удаленные', click: function() {
                  question.showRemoved(false)
               }, mode: 'text', color: 'primary', "></fc-button>
                <!-- /ko -->
              </td>

            </tr>


            <!-- /ko -->
          </tbody>
          <!-- ko if: question.showRemoved -->
          <tbody class="removed-variants">
            <!-- ko foreach: variants -->
            <!-- ko if: $data.is_deleted == '1' -->
            <tr class="question-statistics__variant-statistics-legend-table-row" data-bind="
                                    click: function () { $component.onVariantClick($data); },
                                    class: legendTableRowClass,
                                    css: { 'question-statistics__variant-statistics-legend-table-row--disabled': !enabled() }
                                ">
              <td class="question-statistics__variant-statistics-legend-table-text-cell">
                <div class="question-statistics__variant-statistics-legend-table-text-cell-content">
                  <div class="question-statistics__variant-statistics-legend-table-color-indicator" data-bind="style: { backgroundColor: $component.colors[$index()] }">
                  </div>
                  <span>
                    <span>
                      <!-- ko text: text -->
                      <!-- /ko -->
                      <!-- ko if: $parent.question.correctVariantsIds.includes($data.id) -->
                      <svg-icon params="name: 'check'" class="svg-icon--sm f-color-text ml-1"></svg-icon>
                      <!-- /ko -->
                    </span>
                    <!-- ko if: $data.linkWithClientField -->
                    <small class="question-statistics__service-text" data-bind="text: '(связан с параметром ' + $data.linkedClientField + ' контакта)'"></small>
                    <!-- /ko -->
                  </span>
                </div>
              </td>
              <!-- ko if: $parent.withPoints -->
              <td align="right" class="question-statistics__variant-statistics-legend-table-vote-count-cell" data-bind="text: $data.points || 0">
              </td>
              <!-- /ko -->
              <td align="right" class="question-statistics__variant-statistics-legend-table-vote-count-cell" data-bind="text: $component.value[$index()]">
              </td>
              <td align="right" class="question-statistics__variant-statistics-legend-table-percentage-cell" data-bind="text: $component.getPercent($component.value[$index()])">
              </td>
            </tr>
            <!-- /ko -->
            <!-- /ko -->


            <!-- ko if: question.showRemoved -->
            <tr class="question-statistics__variant-statistics-legend-table-row">
              <td class="question-statistics__variant-statistics-legend-table-text-cell" data-bind="attr: {
                colspan: $parent.withPoints ? 4 : 3
              }">

                <fc-button params="label: 'Скрыть удаленные', click: function() {
                  question.showRemoved(false)
               }, mode: 'text', color: 'primary'"></fc-button>
              </td>

            </tr>
            <!-- /ko -->
          </tbody>
          <!-- /ko -->
        </table>
      </div>
    </div>
    <a class="no-print question-statistics__question-additional-button
      question-statistics__question-all-profiles-button" data-bind="click: onAllVariantsClick">
      Все ответы
    </a>
  </div>
  <!-- /ko -->


</template>
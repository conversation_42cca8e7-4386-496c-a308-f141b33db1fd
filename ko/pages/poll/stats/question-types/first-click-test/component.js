// ko/pages/poll/stats/question-types/first-click-test/component.js

import { QuestionStats } from '../models/component';

class ViewModel extends QuestionStats {
  constructor(params) {
    super({
      name: 'first-click-test',
      defaultChart: 'column', // Default view is column
      charts: ['heatmap', 'column'], // Available chart types for this question
      question: params.question,
    });

    this.imageUrl = this.question.imageUrl;
    this.processedAreas = ko.observableArray(this.question.processedAreas.map(area => ({
      ...area,
      visible: ko.observable(true) // Add observable for visibility
    })));

    this.showAreas = ko.observable(false);

    // Chart mode observable
    this.mode = ko.observable('column'); // Default to column

    // Heatmap specific properties
    this.heatmapInitialized = ko.observable(false);

    // Area filtering for heatmap
    this.allAreasVisible = ko.pureComputed({
        read: () => this.processedAreas().every(area => area.visible()),
        write: (value) => {
            this.processedAreas().forEach(area => area.visible(value));
        }
    });

    // Subscribe to mode changes to initialize charts
    this.mode.subscribe((newMode) => {
      setTimeout(() => { // Timeout to ensure DOM is ready
        if (newMode === 'heatmap' && !this.heatmapInitialized()) this.initHeatmap();
        if (newMode === 'column') this.initColumnChart(); // Renamed from timeBar
      }, 100); // Small delay
    });
  }

  generateHeatmapData() {
    const gridSize = 2; // 2% grid cells
    const heatmapGrid = {};
    
    for (let x = 0; x <= 100; x += gridSize) {
      for (let y = 0; y <= 100; y += gridSize) {
        heatmapGrid[`${x},${y}`] = 0;
      }
    }
    
    this.question.statistics.clickPoints.forEach(point => {
      const gridX = Math.floor(point.x / gridSize) * gridSize;
      const gridY = Math.floor(point.y / gridSize) * gridSize;
      const key = `${gridX},${gridY}`;
      
      if (heatmapGrid[key] !== undefined) {
        heatmapGrid[key]++;
      }
    });
    
    const data = [];
    Object.keys(heatmapGrid).forEach(key => {
      const [x, y] = key.split(',').map(Number);
      if (heatmapGrid[key] > 0) {
        data.push({ x, y, value: heatmapGrid[key] });
      }
    });
    return data;
  }

  toggleAreas() {
    this.showAreas(!this.showAreas());
  }

  toggleAreaVisibility(area) {
    area.visible(!area.visible());
    // This will trigger re-evaluation of allAreasVisible pureComputed
  }
  
  get visibleAreas() {
    return this.processedAreas().filter(area => area.visible());
  }

  // Sidesheet handlers from question model, passed through
  openAreaDetails(area) {
    this.question.openAreaDetails(area);
  }

  openUserPointsDetails() {
    this.question.openUserPointsDetails();
  }

  openSkippedDetails() {
    this.question.openSkippedDetails();
  }
  
  openHeatmapDetailsModal() { // Matched name from template
    this.question.openHeatmapDetailsModal();
  }

  onAllAnswersClick() {
    this.question.onAllAnswersClick();
  }

  initHeatmap() {
    if (this.heatmapInitialized() || !document.getElementById('heatmap-' + this.id)) return;
    
    const img = new Image();
    img.onload = () => {
      const container = document.getElementById('heatmap-' + this.id);
      if (!container) return;
      
      const aspectRatio = img.height / img.width;
      const containerWidth = container.offsetWidth;
      const chartHeight = containerWidth * aspectRatio;
      
      this.createHeatmapChart(containerWidth, chartHeight);
      this.heatmapInitialized(true);
    };
    img.src = this.imageUrl;
  }

  createHeatmapChart(width, height) {
    const heatmapData = this.generateHeatmapData();
    this.charts.heatmap = Highcharts.chart('heatmap-' + this.id, {
      chart: { type: 'heatmap', backgroundColor: 'transparent', width, height, margin: [0,0,0,0], spacing: [0,0,0,0] },
      title: { text: null }, credits: { enabled: false }, legend: { enabled: false },
      xAxis: { min: 0, max: 100, visible: false },
      yAxis: { min: 0, max: 100, reversed: true, visible: false },
      colorAxis: {
        min: 0, minColor: 'rgba(255, 255, 255, 0)', maxColor: 'rgba(255, 0, 0, 0.8)',
        stops: [
          [0, 'rgba(255, 255, 255, 0)'], [0.1, 'rgba(255, 255, 0, 0.3)'],
          [0.5, 'rgba(255, 165, 0, 0.5)'], [0.9, 'rgba(255, 0, 0, 0.7)'],
          [1, 'rgba(139, 0, 0, 0.8)']
        ]
      },
      tooltip: { formatter: function() { return `Кликов в этой области: <b>${this.point.value}</b>`; } },
      series: [{
        type: 'heatmap', data: heatmapData, borderWidth: 0, nullColor: 'transparent',
        turboThreshold: 0, interpolation: true, boostThreshold: 1, colsize: 2, rowsize: 2
      }],
      plotOptions: { heatmap: { interpolation: true } }
    });
  }

  initColumnChart() {
    // Use the columnChartData getter from the Question model
    const seriesData = this.question.columnChartData;
    this.charts.column = Highcharts.chart(this.chartIds.column, {
      chart: { type: 'column', height: 300 },
      title: { text: null },
      credits: { enabled: false },
      legend: { enabled: false },
      xAxis: {
        categories: seriesData.map(item => item.name),
        title: { text: 'Области клика' }
      },
      yAxis: { title: { text: 'Количество кликов' } },
      tooltip: {
        formatter: function() {
          return `${this.x}: <b>${this.y}</b> кликов`;
        }
      },
      plotOptions: {
        column: {
          dataLabels: { enabled: true, format: '{y}' },
          colorByPoint: true
        }
      },
      colors: this.question.colors,
      series: [{
        name: 'Клики',
        data: seriesData.map(item => ({
          name: item.name,
          y: item.data[0],
          color: item.color
        }))
      }]
    });
  }

  onInit() {
    // Initialize default view chart
    if (this.mode() === 'heatmap' && !this.heatmapInitialized()) {
        this.initHeatmap();
    } else if (this.mode() === 'column') {
        this.initColumnChart();
    }
  }
}

// Register the component
ko.components.register('first-click-test-question-stats', {
  viewModel: ViewModel,
  template: {
    element: 'first-click-test-question-stats-template'
  }
});
